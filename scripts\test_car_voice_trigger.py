#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汽车语音触发功能测试脚本

此脚本用于测试汽车语音触发功能的各项特性：
1. 数据包解析测试
2. 语音触发条件测试
3. 协议兼容性测试
4. 性能压力测试

使用方法:
    python test_car_voice_trigger.py --port COM13 --test all
"""

import serial
import time
import argparse
import struct
import json
from typing import List, Dict, Any

class CarDataPacket:
    """汽车数据包类"""
    
    def __init__(self, header: int, data_type: int, payload: bytes):
        self.header = header
        self.data_type = data_type
        self.payload = payload
        self.length = len(payload)
        self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> int:
        """计算校验和"""
        checksum = self.header ^ self.data_type ^ self.length
        for byte in self.payload:
            checksum ^= byte
        return checksum & 0xFF
    
    def to_bytes(self) -> bytes:
        """转换为字节数组"""
        return bytes([self.header, self.data_type, self.length]) + self.payload + bytes([self.checksum])

class CarVoiceTriggerTester:
    """汽车语音触发测试器"""
    
    # 协议常量
    HEADER_360 = 0xAA
    HEADER_CUSTOM = 0x55
    
    # 数据类型
    DATA_TYPE_STATUS = 0x01
    DATA_TYPE_SPEED = 0x02
    DATA_TYPE_DOOR = 0x03
    DATA_TYPE_GEAR = 0x04
    DATA_TYPE_SIGNAL = 0x05
    DATA_TYPE_STEERING = 0x06
    
    def __init__(self, port: str, baudrate: int = 19200):
        """初始化测试器"""
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.test_results = []
    
    def connect(self) -> bool:
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            print(f"✅ 已连接到 {self.port} (波特率: {self.baudrate})")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print("🔌 串口连接已断开")
    
    def send_packet(self, packet: CarDataPacket) -> bool:
        """发送数据包"""
        if not self.serial_conn or not self.serial_conn.is_open:
            print("❌ 串口未连接")
            return False
        
        try:
            data = packet.to_bytes()
            self.serial_conn.write(data)
            print(f"📤 发送数据包: {data.hex().upper()}")
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def test_360_protocol(self) -> Dict[str, Any]:
        """测试360协议"""
        print("\n🧪 测试360协议...")
        results = {"name": "360协议测试", "passed": 0, "failed": 0, "details": []}
        
        # 测试状态数据包
        test_cases = [
            {
                "name": "车门开启状态",
                "payload": bytes([0x01, 0x02, 0x01, 0x00, 0x50, 0x00, 0x00, 0x00]),  # 驾驶员门开启
                "expected": "应触发车门开启语音"
            },
            {
                "name": "左转向灯",
                "payload": bytes([0x01, 0x02, 0x01, 0x00, 0x50, 0x00, 0x00, 0x00]),  # 左转向灯
                "expected": "应触发左转向灯语音"
            },
            {
                "name": "倒车档位",
                "payload": bytes([0x01, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]),  # R档
                "expected": "应触发倒车提醒语音"
            }
        ]
        
        for test_case in test_cases:
            print(f"  📋 测试: {test_case['name']}")
            packet = CarDataPacket(self.HEADER_360, self.DATA_TYPE_STATUS, test_case['payload'])
            
            if self.send_packet(packet):
                print(f"    ✅ 数据包发送成功")
                print(f"    💡 期望结果: {test_case['expected']}")
                results["passed"] += 1
                results["details"].append(f"✅ {test_case['name']}: 发送成功")
                time.sleep(2)  # 等待处理
            else:
                print(f"    ❌ 数据包发送失败")
                results["failed"] += 1
                results["details"].append(f"❌ {test_case['name']}: 发送失败")
        
        return results
    
    def test_custom_protocol(self) -> Dict[str, Any]:
        """测试自定义协议"""
        print("\n🧪 测试自定义协议...")
        results = {"name": "自定义协议测试", "passed": 0, "failed": 0, "details": []}
        
        # 测试自定义协议数据包
        test_cases = [
            {
                "name": "综合状态数据",
                "payload": bytes([0x01, 0x02, 0x00, 0x50, 0x01, 0x01, 0x00, 0x00]),  # ACC开启，D档，80km/h，驾驶员门开启，左转向灯
                "expected": "应触发多个语音提醒"
            },
            {
                "name": "高速行驶",
                "payload": bytes([0x01, 0x02, 0x00, 0x96, 0x00, 0x00, 0x00, 0x00]),  # 150km/h
                "expected": "应触发超速提醒"
            }
        ]
        
        for test_case in test_cases:
            print(f"  📋 测试: {test_case['name']}")
            packet = CarDataPacket(self.HEADER_CUSTOM, self.DATA_TYPE_STATUS, test_case['payload'])
            
            if self.send_packet(packet):
                print(f"    ✅ 数据包发送成功")
                print(f"    💡 期望结果: {test_case['expected']}")
                results["passed"] += 1
                results["details"].append(f"✅ {test_case['name']}: 发送成功")
                time.sleep(2)  # 等待处理
            else:
                print(f"    ❌ 数据包发送失败")
                results["failed"] += 1
                results["details"].append(f"❌ {test_case['name']}: 发送失败")
        
        return results
    
    def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        print("\n🧪 测试错误处理...")
        results = {"name": "错误处理测试", "passed": 0, "failed": 0, "details": []}
        
        # 测试错误数据包
        test_cases = [
            {
                "name": "错误包头",
                "data": bytes([0xFF, 0x01, 0x04, 0x01, 0x02, 0x03, 0x04, 0x00]),
                "expected": "应忽略错误包头"
            },
            {
                "name": "错误校验和",
                "data": bytes([0xAA, 0x01, 0x04, 0x01, 0x02, 0x03, 0x04, 0xFF]),
                "expected": "应检测到校验和错误"
            },
            {
                "name": "超长数据包",
                "data": bytes([0xAA, 0x01, 0xFF] + [0x00] * 255 + [0x00]),
                "expected": "应拒绝超长数据包"
            }
        ]
        
        for test_case in test_cases:
            print(f"  📋 测试: {test_case['name']}")
            
            try:
                if self.serial_conn and self.serial_conn.is_open:
                    self.serial_conn.write(test_case['data'])
                    print(f"    ✅ 错误数据发送成功")
                    print(f"    💡 期望结果: {test_case['expected']}")
                    results["passed"] += 1
                    results["details"].append(f"✅ {test_case['name']}: 发送成功")
                    time.sleep(1)
                else:
                    print(f"    ❌ 串口未连接")
                    results["failed"] += 1
                    results["details"].append(f"❌ {test_case['name']}: 串口未连接")
            except Exception as e:
                print(f"    ❌ 发送失败: {e}")
                results["failed"] += 1
                results["details"].append(f"❌ {test_case['name']}: {e}")
        
        return results
    
    def test_performance(self) -> Dict[str, Any]:
        """测试性能"""
        print("\n🧪 测试性能...")
        results = {"name": "性能测试", "passed": 0, "failed": 0, "details": []}
        
        # 发送大量数据包测试性能
        packet_count = 100
        start_time = time.time()
        success_count = 0
        
        print(f"  📋 发送 {packet_count} 个数据包...")
        
        for i in range(packet_count):
            # 创建测试数据包
            payload = bytes([i % 256, (i + 1) % 256, (i + 2) % 256, (i + 3) % 256])
            packet = CarDataPacket(self.HEADER_360, self.DATA_TYPE_STATUS, payload)
            
            if self.send_packet(packet):
                success_count += 1
            
            time.sleep(0.01)  # 10ms间隔
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"    ✅ 发送完成: {success_count}/{packet_count} 成功")
        print(f"    ⏱️ 耗时: {duration:.2f}秒")
        print(f"    📊 速率: {success_count/duration:.1f} 包/秒")
        
        if success_count >= packet_count * 0.9:  # 90%成功率
            results["passed"] += 1
            results["details"].append(f"✅ 性能测试通过: {success_count}/{packet_count} 成功")
        else:
            results["failed"] += 1
            results["details"].append(f"❌ 性能测试失败: {success_count}/{packet_count} 成功")
        
        return results
    
    def run_all_tests(self) -> List[Dict[str, Any]]:
        """运行所有测试"""
        print("🚀 开始汽车语音触发功能测试...")
        
        if not self.connect():
            return []
        
        try:
            # 等待系统稳定
            print("⏳ 等待系统稳定...")
            time.sleep(3)
            
            # 运行各项测试
            self.test_results = [
                self.test_360_protocol(),
                self.test_custom_protocol(),
                self.test_error_handling(),
                self.test_performance()
            ]
            
            return self.test_results
            
        finally:
            self.disconnect()
    
    def print_summary(self):
        """打印测试总结"""
        if not self.test_results:
            print("❌ 没有测试结果")
            return
        
        print("\n" + "="*50)
        print("📊 测试总结")
        print("="*50)
        
        total_passed = 0
        total_failed = 0
        
        for result in self.test_results:
            print(f"\n📋 {result['name']}")
            print(f"  ✅ 通过: {result['passed']}")
            print(f"  ❌ 失败: {result['failed']}")
            
            for detail in result['details']:
                print(f"    {detail}")
            
            total_passed += result['passed']
            total_failed += result['failed']
        
        print(f"\n🎯 总计:")
        print(f"  ✅ 通过: {total_passed}")
        print(f"  ❌ 失败: {total_failed}")
        print(f"  📊 成功率: {total_passed/(total_passed+total_failed)*100:.1f}%")
        
        if total_failed == 0:
            print("\n🎉 所有测试通过！汽车语音触发功能工作正常。")
        else:
            print(f"\n⚠️ 有 {total_failed} 项测试失败，请检查系统配置和硬件连接。")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="汽车语音触发功能测试脚本")
    parser.add_argument("--port", "-p", default="COM13", help="串口端口 (默认: COM13)")
    parser.add_argument("--baudrate", "-b", type=int, default=19200, help="波特率 (默认: 19200)")
    parser.add_argument("--test", "-t", choices=["all", "360", "custom", "error", "performance"], 
                       default="all", help="测试类型 (默认: all)")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = CarVoiceTriggerTester(args.port, args.baudrate)
    
    # 运行测试
    if args.test == "all":
        tester.run_all_tests()
    else:
        if not tester.connect():
            return
        
        try:
            if args.test == "360":
                tester.test_results = [tester.test_360_protocol()]
            elif args.test == "custom":
                tester.test_results = [tester.test_custom_protocol()]
            elif args.test == "error":
                tester.test_results = [tester.test_error_handling()]
            elif args.test == "performance":
                tester.test_results = [tester.test_performance()]
        finally:
            tester.disconnect()
    
    # 打印结果
    tester.print_summary()

if __name__ == "__main__":
    main()
