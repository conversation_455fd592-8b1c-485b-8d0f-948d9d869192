#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证是否是真正的Opus数据包
"""

import struct
import os
import sys
import numpy as np

def verify_real_opus_data(file_path):
    """
    验证P3文件中是否包含真正的Opus数据
    """
    print(f"🔬 验证真正的Opus数据: {file_path}")
    print("=" * 70)
    
    packets = read_p3_packets(file_path)
    
    print(f"📦 总数据包数: {len(packets)}")
    
    # 1. 检查TOC字节一致性
    check_toc_consistency(packets)
    
    # 2. 检查数据包内容特征
    check_packet_content_features(packets)
    
    # 3. 检查是否有真实Opus编码特征
    check_real_opus_features(packets)
    
    # 4. 与999.p3对比
    compare_with_reference(packets)
    
    # 5. 生成最终判断
    generate_final_verdict(packets)

def read_p3_packets(file_path):
    """
    读取P3文件中的数据包
    """
    packets = []
    
    with open(file_path, 'rb') as f:
        while True:
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
                
            packets.append(packet_data)
    
    return packets

def check_toc_consistency(packets):
    """
    检查TOC字节一致性
    """
    print("\n1️⃣  TOC字节一致性检查")
    print("-" * 50)
    
    toc_bytes = [p[0] for p in packets if len(p) > 0]
    unique_tocs = set(toc_bytes)
    
    print(f"TOC字节种类: {len(unique_tocs)}")
    print(f"TOC字节值: {[f'0x{b:02X}' for b in unique_tocs]}")
    
    if len(unique_tocs) == 1 and 0x58 in unique_tocs:
        print("✅ TOC字节完全一致，全部为0x58")
        return True
    else:
        print("❌ TOC字节不一致或不是0x58")
        return False

def check_packet_content_features(packets):
    """
    检查数据包内容特征
    """
    print("\n2️⃣  数据包内容特征检查")
    print("-" * 50)
    
    # 收集所有字节（跳过TOC）
    all_bytes = []
    for packet in packets:
        if len(packet) > 1:
            all_bytes.extend(packet[1:])
    
    if not all_bytes:
        print("❌ 没有足够的数据进行分析")
        return False
    
    # 字节分布分析
    byte_freq = np.bincount(all_bytes, minlength=256)
    
    # 计算卡方统计量
    expected_freq = len(all_bytes) / 256
    chi_square = np.sum((byte_freq - expected_freq) ** 2 / expected_freq)
    
    print(f"字节分布卡方值: {chi_square:.1f}")
    
    # 信息熵
    byte_probs = byte_freq / np.sum(byte_freq)
    entropy = -np.sum(byte_probs * np.log2(byte_probs + 1e-10))
    
    print(f"信息熵: {entropy:.3f} bits/byte (最大8.0)")
    
    # 最常见字节
    top_bytes = sorted(enumerate(byte_freq), key=lambda x: x[1], reverse=True)[:5]
    print(f"最常见字节:")
    for byte_val, count in top_bytes:
        percentage = (count / len(all_bytes)) * 100
        print(f"  0x{byte_val:02X}: {percentage:.1f}%")
    
    # 判断标准
    is_random = chi_square < 400  # 调整阈值
    is_high_entropy = entropy > 7.5
    is_balanced = all(count / len(all_bytes) < 0.02 for _, count in top_bytes)  # 没有字节超过2%
    
    print(f"\n特征评估:")
    print(f"  随机性: {'✅' if is_random else '❌'} (卡方值 < 400)")
    print(f"  高熵性: {'✅' if is_high_entropy else '❌'} (熵 > 7.5)")
    print(f"  平衡性: {'✅' if is_balanced else '❌'} (无字节 > 2%)")
    
    return is_random and is_high_entropy and is_balanced

def check_real_opus_features(packets):
    """
    检查真实Opus编码特征
    """
    print("\n3️⃣  真实Opus编码特征检查")
    print("-" * 50)
    
    # 检查数据包大小分布
    sizes = [len(p) for p in packets]
    
    print(f"数据包大小:")
    print(f"  范围: {min(sizes)}-{max(sizes)} 字节")
    print(f"  平均: {np.mean(sizes):.1f} 字节")
    print(f"  标准差: {np.std(sizes):.1f}")
    
    # 检查大小变化模式
    if len(sizes) > 1:
        size_diffs = np.diff(sizes)
        size_variation = np.std(size_diffs)
        print(f"  大小变化标准差: {size_variation:.1f}")
        
        # 真实Opus应该有合理的大小变化
        has_variation = size_variation > 50  # 有足够的变化
        print(f"  大小变化合理: {'✅' if has_variation else '❌'}")
    
    # 检查是否有重复的数据包
    unique_packets = len(set(packets))
    duplicate_ratio = 1 - (unique_packets / len(packets))
    
    print(f"数据包唯一性:")
    print(f"  唯一数据包: {unique_packets}/{len(packets)}")
    print(f"  重复率: {duplicate_ratio:.1%}")
    
    has_uniqueness = duplicate_ratio < 0.1  # 重复率小于10%
    print(f"  唯一性良好: {'✅' if has_uniqueness else '❌'}")
    
    # 检查数据包内部结构
    has_structure = check_opus_internal_structure(packets[:5])
    
    return has_variation and has_uniqueness and has_structure

def check_opus_internal_structure(sample_packets):
    """
    检查Opus数据包内部结构
    """
    print(f"内部结构检查 (前5个数据包):")
    
    structure_score = 0
    
    for i, packet in enumerate(sample_packets):
        if len(packet) < 10:
            continue
            
        # 检查第二字节是否合理
        second_byte = packet[1]
        
        # 检查是否有明显的重复模式
        has_repetition = False
        for j in range(len(packet) - 3):
            if packet[j] == packet[j+1] == packet[j+2] == packet[j+3]:
                has_repetition = True
                break
        
        # 检查字节分布
        byte_values = list(packet[1:min(17, len(packet))])
        byte_std = np.std(byte_values) if len(byte_values) > 1 else 0
        
        print(f"  数据包 {i+1}: 第二字节=0x{second_byte:02X}, 重复={'❌' if has_repetition else '✅'}, 标准差={byte_std:.1f}")
        
        if not has_repetition and byte_std > 30:
            structure_score += 1
    
    structure_good = structure_score >= len(sample_packets) * 0.6
    print(f"  结构评分: {structure_score}/{len(sample_packets)} ({'✅' if structure_good else '❌'})")
    
    return structure_good

def compare_with_reference(packets):
    """
    与999.p3参考文件对比
    """
    print("\n4️⃣  与999.p3参考对比")
    print("-" * 50)
    
    try:
        ref_packets = read_p3_packets("999.p3")
        
        # 对比数据包数量
        print(f"数据包数量: {len(packets)} vs {len(ref_packets)} (参考)")
        
        # 对比大小分布
        sizes = [len(p) for p in packets]
        ref_sizes = [len(p) for p in ref_packets]
        
        print(f"大小范围: {min(sizes)}-{max(sizes)} vs {min(ref_sizes)}-{max(ref_sizes)} (参考)")
        
        # 对比字节分布特征
        all_bytes = []
        for packet in packets:
            if len(packet) > 1:
                all_bytes.extend(packet[1:])
        
        ref_all_bytes = []
        for packet in ref_packets:
            if len(packet) > 1:
                ref_all_bytes.extend(packet[1:])
        
        if all_bytes and ref_all_bytes:
            byte_freq = np.bincount(all_bytes, minlength=256)
            ref_byte_freq = np.bincount(ref_all_bytes, minlength=256)
            
            # 计算分布相似性
            byte_probs = byte_freq / np.sum(byte_freq)
            ref_byte_probs = ref_byte_freq / np.sum(ref_byte_freq)
            
            # KL散度
            kl_div = np.sum(byte_probs * np.log((byte_probs + 1e-10) / (ref_byte_probs + 1e-10)))
            
            print(f"字节分布KL散度: {kl_div:.3f} (越小越相似)")
            
            similarity_good = kl_div < 0.5
            print(f"分布相似性: {'✅' if similarity_good else '❌'}")
            
            return similarity_good
    
    except Exception as e:
        print(f"❌ 无法读取参考文件: {e}")
        return False
    
    return False

def generate_final_verdict(packets):
    """
    生成最终判断
    """
    print("\n" + "=" * 70)
    print("🎯 最终判断")
    print("=" * 70)
    
    # 重新运行所有检查
    toc_ok = check_toc_consistency(packets)
    content_ok = check_packet_content_features(packets)
    opus_ok = check_real_opus_features(packets)
    ref_ok = compare_with_reference(packets)
    
    # 计算总分
    total_score = sum([toc_ok, content_ok, opus_ok, ref_ok])
    
    print(f"\n📊 评估结果:")
    print(f"  TOC一致性: {'✅' if toc_ok else '❌'}")
    print(f"  内容特征: {'✅' if content_ok else '❌'}")
    print(f"  Opus特征: {'✅' if opus_ok else '❌'}")
    print(f"  参考相似性: {'✅' if ref_ok else '❌'}")
    print(f"  总分: {total_score}/4")
    
    if total_score >= 3:
        print("\n🎉 判断: 这是真正的Opus编码数据！")
        print("✅ 数据质量优秀，应该能在小智系统上正常播放")
    elif total_score >= 2:
        print("\n⚠️  判断: 可能是真正的Opus数据，但有一些问题")
        print("🔧 建议进一步优化转换过程")
    else:
        print("\n❌ 判断: 这不是真正的Opus编码数据")
        print("🚨 需要重新实现转换器")
    
    return total_score >= 3

def main():
    if len(sys.argv) != 2:
        print("使用方法: python verify_real_opus.py <p3文件>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        sys.exit(1)
    
    is_real = verify_real_opus_data(file_path)
    sys.exit(0 if is_real else 1)

if __name__ == "__main__":
    main()
