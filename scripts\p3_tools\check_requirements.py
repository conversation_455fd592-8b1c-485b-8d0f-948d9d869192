#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查P3文件是否符合具体要求
"""

import struct
import os
import sys
import numpy as np

def check_specific_requirements(file_path):
    """
    检查P3文件是否符合具体要求：
    - 音频源: 24000Hz, 单声道, 完整音频长度（不截取）
    - Opus编码: 64kbps, 60ms帧, 复杂度5
    - 输出: 变化的数据包大小 (60-200字节)
    """
    print(f"🔍 检查具体要求符合性: {file_path}")
    print("=" * 70)
    
    packets = read_p3_packets(file_path)
    
    print(f"📦 总数据包数: {len(packets)}")
    
    # 1. 检查音频参数
    check_audio_parameters(packets)
    
    # 2. 检查Opus编码参数
    check_opus_encoding_parameters(packets)
    
    # 3. 检查数据包大小变化
    check_packet_size_variation(packets)
    
    # 4. 生成符合性报告
    generate_requirements_report(packets)

def read_p3_packets(file_path):
    """
    读取P3文件中的数据包
    """
    packets = []
    
    with open(file_path, 'rb') as f:
        while True:
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
                
            packets.append(packet_data)
    
    return packets

def check_audio_parameters(packets):
    """
    检查音频参数
    """
    print("\n1️⃣  音频参数检查")
    print("-" * 50)
    
    # 推断采样率和时长
    frame_duration_ms = 60  # 60ms帧
    total_duration = len(packets) * frame_duration_ms / 1000
    
    print(f"推断音频参数:")
    print(f"  数据包数量: {len(packets)}")
    print(f"  帧时长: {frame_duration_ms}ms")
    print(f"  总时长: {total_duration:.2f} 秒")
    
    # 检查TOC字节确认单声道
    if packets:
        toc = packets[0][0]
        stereo = (toc >> 2) & 0x01
        channels = 2 if stereo else 1
        
        print(f"  声道数: {channels} ({'单声道' if channels == 1 else '立体声'})")
        
        # 基于60ms帧推断采样率
        # 如果是24000Hz，60ms = 1440样本
        # 如果是16000Hz，60ms = 960样本
        
        # 通过数据包大小推断可能的采样率
        sizes = [len(p) for p in packets]
        avg_size = np.mean(sizes)
        
        if avg_size > 100:
            likely_sample_rate = "24000Hz (基于数据包大小推断)"
        else:
            likely_sample_rate = "16000Hz (基于数据包大小推断)"
        
        print(f"  推断采样率: {likely_sample_rate}")
        
        # 检查是否完整音频（不截取）
        if total_duration > 1.0:  # 超过1秒认为是完整音频
            print(f"  ✅ 音频长度: 完整 ({total_duration:.2f}秒)")
        else:
            print(f"  ⚠️  音频长度: 可能被截取 ({total_duration:.2f}秒)")

def check_opus_encoding_parameters(packets):
    """
    检查Opus编码参数
    """
    print("\n2️⃣  Opus编码参数检查")
    print("-" * 50)
    
    if not packets:
        print("❌ 没有数据包")
        return
    
    # 检查TOC字节
    toc_bytes = [p[0] for p in packets if len(p) > 0]
    unique_tocs = set(toc_bytes)
    
    print(f"TOC字节分析:")
    for toc in unique_tocs:
        config = (toc >> 3) & 0x1F
        stereo = (toc >> 2) & 0x01
        frame_count = toc & 0x03
        
        # 解析配置
        if config == 11:
            mode = "SILK WB"
            bandwidth = "宽带"
            frame_size = "60ms"
        else:
            mode = f"Config {config}"
            bandwidth = "未知"
            frame_size = "未知"
        
        print(f"  TOC=0x{toc:02X}: {mode}, {bandwidth}, {frame_size}")
        print(f"    配置: {config}")
        print(f"    声道: {'立体声' if stereo else '单声道'}")
        print(f"    帧数: {frame_count}")
    
    # 检查是否符合要求
    if len(unique_tocs) == 1 and 0x58 in unique_tocs:
        print(f"  ✅ Opus配置: 符合要求 (Config 11, SILK WB 60ms)")
    else:
        print(f"  ❌ Opus配置: 不符合要求")
    
    # 检查比特率（通过数据包大小估算）
    sizes = [len(p) for p in packets]
    avg_size = np.mean(sizes)
    
    # 估算比特率: (平均包大小 * 8 bits) / (60ms) = kbps
    estimated_bitrate = (avg_size * 8) / 0.06 / 1000
    
    print(f"编码参数估算:")
    print(f"  平均数据包大小: {avg_size:.1f} 字节")
    print(f"  估算比特率: {estimated_bitrate:.1f} kbps")
    
    if 50 <= estimated_bitrate <= 80:
        print(f"  ✅ 比特率: 接近64kbps要求")
    else:
        print(f"  ⚠️  比特率: 可能不符合64kbps要求")

def check_packet_size_variation(packets):
    """
    检查数据包大小变化
    """
    print("\n3️⃣  数据包大小变化检查")
    print("-" * 50)
    
    sizes = [len(p) for p in packets]
    
    print(f"大小统计:")
    print(f"  最小: {min(sizes)} 字节")
    print(f"  最大: {max(sizes)} 字节")
    print(f"  平均: {np.mean(sizes):.1f} 字节")
    print(f"  标准差: {np.std(sizes):.1f}")
    
    # 检查是否在60-200字节范围内
    in_range = all(60 <= size <= 200 for size in sizes)
    
    if in_range:
        print(f"  ✅ 大小范围: 符合要求 (60-200字节)")
    else:
        out_of_range = [size for size in sizes if not (60 <= size <= 200)]
        print(f"  ❌ 大小范围: 不符合要求")
        print(f"    超出范围的大小: {out_of_range[:10]}...")  # 只显示前10个
    
    # 检查变化性
    unique_sizes = len(set(sizes))
    variation_ratio = unique_sizes / len(sizes)
    
    print(f"大小变化性:")
    print(f"  唯一大小数: {unique_sizes}/{len(sizes)}")
    print(f"  变化比例: {variation_ratio:.1%}")
    
    if variation_ratio > 0.3:  # 超过30%的数据包有不同大小
        print(f"  ✅ 大小变化: 良好的变化性")
    else:
        print(f"  ⚠️  大小变化: 变化性不足")
    
    # 显示大小分布
    print(f"大小分布 (前10个): {sizes[:10]}")

def generate_requirements_report(packets):
    """
    生成要求符合性报告
    """
    print("\n" + "=" * 70)
    print("📋 要求符合性报告")
    print("=" * 70)
    
    scores = []
    
    # 1. 音频源检查
    total_duration = len(packets) * 0.06
    if total_duration > 1.0:
        scores.append(("音频长度", True, "完整音频，未截取"))
    else:
        scores.append(("音频长度", False, "可能被截取"))
    
    # 2. 采样率检查（通过数据包大小推断）
    sizes = [len(p) for p in packets]
    avg_size = np.mean(sizes)
    if avg_size > 100:
        scores.append(("采样率", True, "推断为24000Hz"))
    else:
        scores.append(("采样率", False, "推断为16000Hz"))
    
    # 3. 单声道检查
    if packets:
        toc = packets[0][0]
        stereo = (toc >> 2) & 0x01
        scores.append(("声道配置", not stereo, "单声道" if not stereo else "立体声"))
    
    # 4. Opus配置检查
    toc_bytes = [p[0] for p in packets if len(p) > 0]
    config_ok = len(set(toc_bytes)) == 1 and 0x58 in toc_bytes
    scores.append(("Opus配置", config_ok, "Config 11 SILK WB 60ms" if config_ok else "配置不正确"))
    
    # 5. 比特率检查
    estimated_bitrate = (avg_size * 8) / 0.06 / 1000
    bitrate_ok = 50 <= estimated_bitrate <= 80
    scores.append(("比特率", bitrate_ok, f"~{estimated_bitrate:.0f}kbps"))
    
    # 6. 数据包大小范围检查
    size_range_ok = all(60 <= size <= 200 for size in sizes)
    scores.append(("数据包大小", size_range_ok, f"{min(sizes)}-{max(sizes)}字节"))
    
    # 7. 大小变化性检查
    variation_ratio = len(set(sizes)) / len(sizes)
    variation_ok = variation_ratio > 0.3
    scores.append(("大小变化性", variation_ok, f"{variation_ratio:.1%}变化"))
    
    # 显示结果
    passed = sum(1 for _, ok, _ in scores if ok)
    total = len(scores)
    
    print(f"检查项目:")
    for name, ok, detail in scores:
        status = "✅" if ok else "❌"
        print(f"  {status} {name}: {detail}")
    
    print(f"\n🎯 总体符合性: {passed}/{total} ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 完全符合所有要求！")
    elif passed >= total * 0.8:
        print("✅ 基本符合要求，有少量问题")
    else:
        print("⚠️  存在多个问题，需要进一步优化")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python check_requirements.py <p3文件>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        sys.exit(1)
    
    check_specific_requirements(file_path)

if __name__ == "__main__":
    main()
