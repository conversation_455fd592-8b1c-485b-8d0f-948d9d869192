#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析999.p3的内部结构，找出关键特征
"""

import struct
import os

def analyze_999_structure():
    """
    深度分析999.p3的结构
    """
    print("🔍 深度分析999.p3的内部结构")
    print("=" * 60)
    
    with open("999.p3", 'rb') as f:
        packet_index = 0
        
        while True:
            # 读取头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            # 读取数据包
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
            
            print(f"\n📦 数据包 {packet_index + 1}:")
            print(f"   大小: {data_len} 字节")
            print(f"   前32字节: {packet_data[:32].hex()}")
            
            if len(packet_data) > 0:
                # 分析TOC字节
                toc = packet_data[0]
                config = (toc >> 3) & 0x1F
                stereo = (toc >> 2) & 0x01
                frame_count = toc & 0x03
                
                print(f"   TOC: 0x{toc:02X} (config={config}, stereo={stereo}, frames={frame_count})")
                
                # 分析数据包内容模式
                analyze_packet_content(packet_data, packet_index + 1)
            
            packet_index += 1
            
            if packet_index >= 5:  # 只分析前5个数据包
                break
    
    print(f"\n总共分析了 {packet_index} 个数据包")

def analyze_packet_content(packet_data, packet_num):
    """
    分析数据包内容的模式
    """
    if len(packet_data) < 10:
        return
    
    # 字节频率分析
    byte_freq = {}
    for byte in packet_data:
        byte_freq[byte] = byte_freq.get(byte, 0) + 1
    
    # 最常见的字节
    top_bytes = sorted(byte_freq.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"   最常见字节: {[(f'0x{b:02X}', c) for b, c in top_bytes]}")
    
    # 连续字节模式
    consecutive_patterns = find_consecutive_patterns(packet_data)
    if consecutive_patterns:
        print(f"   连续模式: {consecutive_patterns}")
    
    # 重复序列
    repeated_sequences = find_repeated_sequences(packet_data)
    if repeated_sequences:
        print(f"   重复序列: {repeated_sequences}")
    
    # 数据分布
    data_range = f"0x{min(packet_data):02X}-0x{max(packet_data):02X}"
    print(f"   数据范围: {data_range}")
    
    # 特殊模式检测
    special_patterns = detect_special_patterns(packet_data)
    if special_patterns:
        print(f"   特殊模式: {special_patterns}")

def find_consecutive_patterns(data):
    """
    查找连续字节模式
    """
    patterns = []
    i = 0
    while i < len(data) - 2:
        if data[i] == data[i+1] == data[i+2]:
            count = 3
            while i + count < len(data) and data[i + count] == data[i]:
                count += 1
            patterns.append(f"{count}x0x{data[i]:02X}")
            i += count
        else:
            i += 1
    return patterns

def find_repeated_sequences(data):
    """
    查找重复序列
    """
    sequences = []
    for length in [2, 3, 4]:
        for i in range(len(data) - length * 2):
            seq = data[i:i+length]
            if data[i+length:i+length*2] == seq:
                sequences.append(f"{seq.hex()}x2")
                break
    return sequences

def detect_special_patterns(data):
    """
    检测特殊模式
    """
    patterns = []
    
    # 检测递增/递减序列
    for i in range(len(data) - 3):
        if (data[i] + 1 == data[i+1] and 
            data[i+1] + 1 == data[i+2] and 
            data[i+2] + 1 == data[i+3]):
            patterns.append(f"递增序列@{i}")
        elif (data[i] - 1 == data[i+1] and 
              data[i+1] - 1 == data[i+2] and 
              data[i+2] - 1 == data[i+3]):
            patterns.append(f"递减序列@{i}")
    
    # 检测位模式
    if all(b & 0x80 for b in data[1:10]):  # 前10个字节高位都是1
        patterns.append("高位模式")
    
    if all(b < 0x80 for b in data[1:10]):  # 前10个字节高位都是0
        patterns.append("低位模式")
    
    return patterns

def extract_999_template():
    """
    提取999.p3的模板特征
    """
    print("\n🎯 提取999.p3模板特征")
    print("=" * 60)
    
    packets = []
    with open("999.p3", 'rb') as f:
        while True:
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
                
            packets.append(packet_data)
    
    print(f"总数据包: {len(packets)}")
    
    # 分析大小模式
    sizes = [len(p) for p in packets]
    print(f"大小序列: {sizes}")
    
    # 分析大小变化模式
    if len(sizes) > 1:
        diffs = [sizes[i+1] - sizes[i] for i in range(len(sizes)-1)]
        print(f"大小变化: {diffs}")
    
    # 分析TOC字节一致性
    toc_bytes = [p[0] for p in packets if len(p) > 0]
    print(f"TOC字节: {[f'0x{b:02X}' for b in toc_bytes]}")
    
    # 分析第二字节模式
    second_bytes = [p[1] for p in packets if len(p) > 1]
    print(f"第二字节: {[f'0x{b:02X}' for b in second_bytes]}")
    
    # 保存第一个数据包作为模板
    if packets:
        with open("999_template_packet.bin", "wb") as f:
            f.write(packets[0])
        print(f"已保存第一个数据包模板: 999_template_packet.bin")
    
    return packets

if __name__ == "__main__":
    analyze_999_structure()
    extract_999_template()
