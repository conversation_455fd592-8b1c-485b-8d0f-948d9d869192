#include "car_voice_trigger.h"
#include "car_data_parser.h"
#include "network_console.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <string.h>
#include <sys/stat.h>

// 前向声明Application类
#ifdef __cplusplus
extern "C" {
#endif

// C接口函数声明，用于调用C++的Application类
esp_err_t car_voice_play_p3_file(const char* file_path);

#ifdef __cplusplus
}
#endif

static const char* TAG = "CarVoiceTrigger";

// 全局变量
static bool g_initialized = false;
static bool g_enabled = false;
static bool g_uart_enabled = false;
static bool g_voice_enabled = true;


static TaskHandle_t g_car_data_task_handle = NULL;
static QueueHandle_t g_car_uart_queue = NULL;
static QueueHandle_t g_voice_play_queue = NULL;

static car_data_parser_t g_parser;
static vehicle_state_t g_current_state;
static vehicle_state_t g_previous_state;
static car_voice_system_status_t g_system_status;

// 前向声明
static void car_data_task(void* arg);
static void voice_play_task(void* arg);
static esp_err_t setup_uart(void);
static esp_err_t cleanup_uart(void);
static bool check_voice_triggers(void);
static esp_err_t queue_voice_play(uint8_t voice_id, uint8_t priority);

// 智能数据处理函数声明
static bool is_car_data(const uint8_t* data, size_t len);
static void process_received_data(const uint8_t* data, size_t len);
static void process_car_voice_data(const uint8_t* data, size_t len);


// 语音触发条件检查函数声明
static bool check_acc_welcome(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_r_gear_reminder(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_d_gear_reminder(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_driver_door_open(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_passenger_door_open(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_rear_left_door_open(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_rear_right_door_open(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_driver_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_passenger_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_rear_left_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_rear_right_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_parking_remind(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_steering_warning(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_fatigue_driving(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_take_items(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_steering_center(const vehicle_state_t* current, const vehicle_state_t* previous);
static bool check_turn_signal_long(const vehicle_state_t* current, const vehicle_state_t* previous);

// 语音触发规则表
static voice_trigger_rule_t g_voice_triggers[] = {
    {1,  TRIGGER_ACC_ON,           PRIORITY_NORMAL, 30000, 0, check_acc_welcome},
    {2,  TRIGGER_R_GEAR,           PRIORITY_HIGH,   5000,  0, check_r_gear_reminder},
    {3,  TRIGGER_D_GEAR,           PRIORITY_NORMAL, 5000,  0, check_d_gear_reminder},
    {4,  TRIGGER_DRIVER_DOOR,      PRIORITY_NORMAL, 3000,  0, check_driver_door_open},
    {5,  TRIGGER_PASSENGER_DOOR,   PRIORITY_NORMAL, 3000,  0, check_passenger_door_open},
    {6,  TRIGGER_REAR_LEFT_DOOR,   PRIORITY_NORMAL, 3000,  0, check_rear_left_door_open},
    {7,  TRIGGER_REAR_RIGHT_DOOR,  PRIORITY_NORMAL, 3000,  0, check_rear_right_door_open},
    {8,  TRIGGER_DOOR_WARNING,     PRIORITY_HIGH,   1000,  0, check_driver_door_warning},
    {9,  TRIGGER_DOOR_WARNING,     PRIORITY_HIGH,   1000,  0, check_passenger_door_warning},
    {10, TRIGGER_DOOR_WARNING,     PRIORITY_HIGH,   1000,  0, check_rear_left_door_warning},
    {11, TRIGGER_DOOR_WARNING,     PRIORITY_HIGH,   1000,  0, check_rear_right_door_warning},
    {12, TRIGGER_PARKING_REMIND,   PRIORITY_NORMAL, 3600000, 0, check_parking_remind},
    {13, TRIGGER_STEERING_WARNING, PRIORITY_HIGH,   30000, 0, check_steering_warning},
    {14, TRIGGER_FATIGUE_DRIVING,  PRIORITY_HIGH,   7200000, 0, check_fatigue_driving},
    {15, TRIGGER_TAKE_ITEMS,       PRIORITY_NORMAL, 10000, 0, check_take_items},
    {16, TRIGGER_STEERING_CENTER,  PRIORITY_NORMAL, 5000,  0, check_steering_center},
    {17, TRIGGER_TURN_SIGNAL_LONG, PRIORITY_NORMAL, 20000, 0, check_turn_signal_long},
};

#define VOICE_TRIGGER_COUNT (sizeof(g_voice_triggers) / sizeof(voice_trigger_rule_t))

esp_err_t car_voice_trigger_init(void) {
    if (g_initialized) {
        ESP_LOGW(TAG, "Car voice trigger already initialized");
        return ESP_OK;
    }

    // 设置日志级别为DEBUG，确保所有调试信息都能输出
    esp_log_level_set(TAG, ESP_LOG_DEBUG);

    ESP_LOGI(TAG, "Initializing Car Voice Trigger System...");

    // 初始化系统状态
    memset(&g_system_status, 0, sizeof(g_system_status));
    memset(&g_current_state, 0, sizeof(g_current_state));
    memset(&g_previous_state, 0, sizeof(g_previous_state));

    // 初始化数据解析器
    esp_err_t ret = car_data_parser_init(&g_parser);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize data parser: %s", esp_err_to_name(ret));
        return ret;
    }

    // 创建UART队列
    g_car_uart_queue = xQueueCreate(CAR_DATA_QUEUE_SIZE, sizeof(uart_event_t));
    if (g_car_uart_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create UART queue");
        return ESP_ERR_NO_MEM;
    }

    g_voice_play_queue = xQueueCreate(10, sizeof(voice_play_request_t));
    if (g_voice_play_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create voice play queue");
        return ESP_ERR_NO_MEM;
    }

    // 设置初始状态
    g_initialized = true;
    g_enabled = false;
    g_uart_enabled = false;
    g_voice_enabled = true;
    g_system_status.initialized = true;

    ESP_LOGI(TAG, "Car Voice Trigger System initialized successfully");
    ESP_LOGI(TAG, "Voice trigger rules loaded: %d", VOICE_TRIGGER_COUNT);

    return ESP_OK;
}

esp_err_t car_voice_trigger_deinit(void) {
    if (!g_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Deinitializing Car Voice Trigger System...");

    // 停止功能
    car_voice_trigger_stop();

    // 清理队列
    if (g_car_uart_queue) {
        vQueueDelete(g_car_uart_queue);
        g_car_uart_queue = NULL;
    }

    if (g_voice_play_queue) {
        vQueueDelete(g_voice_play_queue);
        g_voice_play_queue = NULL;
    }

    // 重置状态
    g_initialized = false;
    g_enabled = false;
    g_uart_enabled = false;
    memset(&g_system_status, 0, sizeof(g_system_status));

    ESP_LOGI(TAG, "Car Voice Trigger System deinitialized");
    return ESP_OK;
}

esp_err_t car_voice_trigger_start(void) {
    if (!g_initialized) {
        ESP_LOGE(TAG, "System not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_enabled) {
        ESP_LOGW(TAG, "Car voice trigger already started");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Starting Car Voice Trigger System...");

    // 设置UART
    esp_err_t ret = setup_uart();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to setup UART: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启用系统（在创建任务之前）
    g_enabled = true;
    g_uart_enabled = true;
    g_system_status.uart_enabled = true;
    g_system_status.voice_enabled = g_voice_enabled;

    // 创建数据处理任务
    BaseType_t task_ret = xTaskCreate(
        car_data_task,
        "car_data_task",
        4096,
        NULL,
        5,
        &g_car_data_task_handle
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create car data task");
        cleanup_uart();
        return ESP_ERR_NO_MEM;
    }

    // 创建语音播放任务
    TaskHandle_t voice_task_handle;
    task_ret = xTaskCreate(
        voice_play_task,
        "voice_play_task",
        4096,
        NULL,
        4,
        &voice_task_handle
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create voice play task");
        if (g_car_data_task_handle) {
            vTaskDelete(g_car_data_task_handle);
            g_car_data_task_handle = NULL;
        }
        cleanup_uart();
        return ESP_ERR_NO_MEM;
    }



    ESP_LOGI(TAG, "Car Voice Trigger System started successfully");
    ESP_LOGI(TAG, "UART2 configured: TX=%d, RX=%d, Baud=%d",
             CAR_UART_TX_PIN, CAR_UART_RX_PIN, CAR_UART_BAUD_RATE);

    return ESP_OK;
}

esp_err_t car_voice_trigger_stop(void) {
    if (!g_enabled) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping Car Voice Trigger System...");

    g_enabled = false;
    g_uart_enabled = false;

    // 停止任务
    if (g_car_data_task_handle) {
        vTaskDelete(g_car_data_task_handle);
        g_car_data_task_handle = NULL;
    }

    // 清理UART
    cleanup_uart();

    g_system_status.uart_enabled = false;

    ESP_LOGI(TAG, "Car Voice Trigger System stopped");
    return ESP_OK;
}

// 配置接口实现
esp_err_t car_voice_set_enabled(bool enabled) {
    if (!g_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (enabled && !g_enabled) {
        return car_voice_trigger_start();
    } else if (!enabled && g_enabled) {
        return car_voice_trigger_stop();
    }

    return ESP_OK;
}

bool car_voice_is_enabled(void) {
    return g_enabled;
}

esp_err_t car_voice_set_uart_enabled(bool enabled) {
    g_uart_enabled = enabled;
    g_system_status.uart_enabled = enabled;
    ESP_LOGI(TAG, "UART %s", enabled ? "enabled" : "disabled");
    return ESP_OK;
}

bool car_voice_is_uart_enabled(void) {
    return g_uart_enabled;
}

// 状态查询接口实现
car_voice_system_status_t car_voice_get_status(void) {
    g_system_status.last_data_time = g_current_state.last_update_time;
    return g_system_status;
}

vehicle_state_t car_voice_get_vehicle_state(void) {
    return g_current_state;
}

// 测试接口实现
esp_err_t car_voice_test_play(uint8_t voice_id) {
    if (voice_id < MIN_VOICE_ID || voice_id > MAX_VOICE_ID) {
        ESP_LOGE(TAG, "Invalid voice ID: %d", voice_id);
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Testing voice playback: %d", voice_id);
    return queue_voice_play(voice_id, PRIORITY_NORMAL);
}

esp_err_t car_voice_test_data_inject(const uint8_t* data, size_t len) {
    if (!g_initialized || !data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Injecting test data: %d bytes", len);

    for (size_t i = 0; i < len; i++) {
        parse_result_t result = car_data_parser_feed(&g_parser, data[i]);
        if (result == PARSE_RESULT_OK && car_data_parser_has_packet(&g_parser)) {
            car_data_packet_t packet = car_data_parser_get_packet(&g_parser);

            // 备份当前状态
            g_previous_state = g_current_state;

            // 更新车辆状态
            if (car_data_update_vehicle_state(&packet, &g_current_state)) {
                g_current_state.last_update_time = esp_timer_get_time() / 1000;
                g_system_status.data_received_count++;

                // 检查触发条件
                check_voice_triggers();
            }
        }
    }

    return ESP_OK;
}

// 调试接口实现
void car_voice_print_status(void) {
    ESP_LOGI(TAG, "=== Car Voice Trigger System Status ===");
    ESP_LOGI(TAG, "Initialized: %s", g_system_status.initialized ? "Yes" : "No");
    ESP_LOGI(TAG, "Enabled: %s", g_enabled ? "Yes" : "No");
    ESP_LOGI(TAG, "UART Enabled: %s", g_system_status.uart_enabled ? "Yes" : "No");
    ESP_LOGI(TAG, "Voice Enabled: %s", g_system_status.voice_enabled ? "Yes" : "No");
    ESP_LOGI(TAG, "Data Received: %lu packets", g_system_status.data_received_count);
    ESP_LOGI(TAG, "Voice Played: %lu times", g_system_status.voice_played_count);
    ESP_LOGI(TAG, "Last Data Time: %lu ms", g_system_status.last_data_time);

    // 打印解析器统计
    car_data_parser_print_stats(&g_parser);
}

void car_voice_print_vehicle_state(void) {
    ESP_LOGI(TAG, "=== Vehicle State ===");
    ESP_LOGI(TAG, "Data Valid: %s", g_current_state.data_valid ? "Yes" : "No");
    ESP_LOGI(TAG, "ACC Status: %s", g_current_state.acc_status ? "ON" : "OFF");
    ESP_LOGI(TAG, "Gear Position: %d", g_current_state.gear_position);
    ESP_LOGI(TAG, "Vehicle Speed: %d km/h", g_current_state.vehicle_speed);
    ESP_LOGI(TAG, "Doors - Driver:%s Passenger:%s RearL:%s RearR:%s",
             g_current_state.doors.driver_door ? "Open" : "Closed",
             g_current_state.doors.passenger_door ? "Open" : "Closed",
             g_current_state.doors.rear_left_door ? "Open" : "Closed",
             g_current_state.doors.rear_right_door ? "Open" : "Closed");
    ESP_LOGI(TAG, "Turn Signals - Left:%s Right:%s",
             g_current_state.turn_signals.left_signal ? "ON" : "OFF",
             g_current_state.turn_signals.right_signal ? "ON" : "OFF");
    ESP_LOGI(TAG, "Steering Angle: %d degrees", g_current_state.steering_angle);
    ESP_LOGI(TAG, "Last Update: %lu ms", g_current_state.last_update_time);
}

// 内部函数实现
static esp_err_t setup_uart(void) {
    // 配置UART参数
    uart_config_t uart_config = {
        .baud_rate = CAR_UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
    };

    // 检查UART驱动是否已安装，如果已安装则跳过
    esp_err_t ret = uart_driver_install(CAR_UART_PORT, CAR_UART_BUF_SIZE, 0,
                                       CAR_DATA_QUEUE_SIZE, &g_car_uart_queue, 0);
    if (ret == ESP_FAIL) {
        // UART驱动已经安装，这是正常情况（可能被UartGpioSwitcher安装）
        ESP_LOGI(TAG, "UART driver already installed, skipping installation");
        ret = ESP_OK;
    } else if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    } else {
        ESP_LOGI(TAG, "UART driver installed successfully");
    }

    // 配置UART参数（如果驱动已存在，重新配置参数）
    ret = uart_param_config(CAR_UART_PORT, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART: %s", esp_err_to_name(ret));
        // 注意：不删除驱动，因为可能被其他模块使用
        return ret;
    }

    // 设置UART引脚（如果驱动已存在，重新设置引脚）
    ret = uart_set_pin(CAR_UART_PORT, CAR_UART_TX_PIN, CAR_UART_RX_PIN,
                       UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        // 注意：不删除驱动，因为可能被其他模块使用
        return ret;
    }

    ESP_LOGI(TAG, "UART setup completed successfully");
    return ESP_OK;
}

static esp_err_t cleanup_uart(void) {
    esp_err_t ret = uart_driver_delete(CAR_UART_PORT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to delete UART driver: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "UART cleanup completed");
    }
    return ret;
}

static void car_data_task(void* arg) {
    uint8_t data[CAR_UART_BUF_SIZE];
    uart_event_t event;

    ESP_LOGI(TAG, "Car data task started");

    while (g_enabled) {
        // 等待UART事件
        if (xQueueReceive(g_car_uart_queue, &event, pdMS_TO_TICKS(100))) {
            ESP_LOGD(TAG, "🚗📨 UART event received: type=%d, size=%d", event.type, event.size);
            switch (event.type) {
                case UART_DATA:
                    if (g_uart_enabled && event.size > 0) {
                        // 读取数据
                        int len = uart_read_bytes(CAR_UART_PORT, data,
                                                event.size, pdMS_TO_TICKS(100));
                        if (len > 0) {
                            // 日志输出控制：数据变化时输出，1秒内不变化则不重复输出
                            static uint8_t last_data[32] = {0};
                            static int last_len = 0;
                            static uint32_t last_log_time = 0;
                            uint32_t current_time = esp_timer_get_time() / 1000;

                            bool data_changed = (len != last_len) || (memcmp(data, last_data, len) != 0);
                            bool should_log = data_changed;  // 只有数据变化时才输出日志

                            // 调试信息：检查数据变化逻辑
                            ESP_LOGI(TAG, "🚗🔍 Debug: len=%d, last_len=%d, memcmp=%d, data_changed=%d",
                                    len, last_len, memcmp(data, last_data, len), data_changed);

                            // 先进行智能识别数据类型
                            if (is_car_data(data, len)) {
                                // 只有汽车数据才记录汽车语音系统日志
                                if (should_log) {
                                    // 添加数据接收日志
                                    ESP_LOGI(TAG, "🚗📡 Received UART data: %d bytes", len);

                                    // 打印接收到的原始数据（十六进制格式）
                                    char hex_str[len * 3 + 1];
                                    for (int i = 0; i < len; i++) {
                                        sprintf(&hex_str[i * 3], "%02X ", data[i]);
                                    }
                                    hex_str[len * 3] = '\0';
                                    ESP_LOGI(TAG, "🚗📡 Raw data: %s", hex_str);

                                    // 更新记录
                                    memcpy(last_data, data, len);
                                    last_len = len;
                                    last_log_time = current_time;
                                }
                            }

                            // 智能识别数据类型并自动路由
                            process_received_data(data, len);

                        }
                    }
                    break;

                case UART_FIFO_OVF:
                    ESP_LOGW(TAG, "UART FIFO overflow");
                    uart_flush_input(CAR_UART_PORT);
                    xQueueReset(g_car_uart_queue);
                    break;

                case UART_BUFFER_FULL:
                    ESP_LOGW(TAG, "UART buffer full");
                    uart_flush_input(CAR_UART_PORT);
                    xQueueReset(g_car_uart_queue);
                    break;

                default:
                    break;
            }
        }

        // 定期检查系统状态
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    ESP_LOGI(TAG, "Car data task stopped");
    vTaskDelete(NULL);
}

static void voice_play_task(void* arg) {
    voice_play_request_t request;

    ESP_LOGI(TAG, "Voice play task started");

    while (g_enabled) {
        if (xQueueReceive(g_voice_play_queue, &request, pdMS_TO_TICKS(100))) {
            if (g_voice_enabled) {
                // 构建文件路径
                char file_path[64];
                snprintf(file_path, sizeof(file_path), "%s%03d.p3",
                        VOICE_FILE_PATH_PREFIX, request.voice_id);

                // 检查文件是否存在
                struct stat st;
                if (stat(file_path, &st) == 0) {
                    ESP_LOGI(TAG, "🚗🔊 Playing car voice: %s (priority: %d)",
                            file_path, request.priority);

                    // 调用音频播放接口
                    esp_err_t ret = car_voice_play_p3_file(file_path);
                    if (ret == ESP_OK) {
                        g_system_status.voice_played_count++;
                        ESP_LOGI(TAG, "✅ Car voice played successfully: %s", file_path);
                    } else {
                        ESP_LOGE(TAG, "❌ Failed to play car voice: %s", file_path);
                    }
                } else {
                    ESP_LOGE(TAG, "❌ Voice file not found: %s", file_path);
                }
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10));
    }

    ESP_LOGI(TAG, "Voice play task stopped");
    vTaskDelete(NULL);
}

static bool check_voice_triggers(void) {
    if (!g_voice_enabled || !g_current_state.data_valid) {
        return false;
    }

    // 防止系统启动时的错误触发：需要有有效的前一状态
    if (!g_previous_state.data_valid) {
        ESP_LOGD(TAG, "🚗⏳ Skipping voice triggers - no previous state available");
        return false;
    }

    uint32_t current_time = esp_timer_get_time() / 1000;
    bool triggered = false;

    for (size_t i = 0; i < VOICE_TRIGGER_COUNT; i++) {
        voice_trigger_rule_t* rule = &g_voice_triggers[i];

        // 检查冷却时间
        if (current_time - rule->last_trigger_time < rule->cooldown_ms) {
            continue;
        }

        // 检查触发条件
        if (rule->check_condition && rule->check_condition(&g_current_state, &g_previous_state)) {
            ESP_LOGI(TAG, "Voice trigger activated: ID=%d, condition=0x%02X",
                    rule->voice_id, rule->trigger_condition);

            // 排队播放语音
            if (queue_voice_play(rule->voice_id, rule->priority) == ESP_OK) {
                rule->last_trigger_time = current_time;
                triggered = true;
            }
        }
    }

    return triggered;
}

static esp_err_t queue_voice_play(uint8_t voice_id, uint8_t priority) {
    voice_play_request_t request = {
        .voice_id = voice_id,
        .priority = priority,
        .timestamp = (uint32_t)(esp_timer_get_time() / 1000)
    };

    if (xQueueSend(g_voice_play_queue, &request, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGW(TAG, "Failed to queue voice play request: %d", voice_id);
        return ESP_ERR_TIMEOUT;
    }

    return ESP_OK;
}

// 触发条件检查函数实现
static bool check_acc_welcome(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // ACC欢迎语3秒延迟机制（文档要求）
    static uint32_t acc_on_time = 0;
    static bool acc_triggered = false;

    // 检测ACC从关闭变为开启
    if ((current->acc_status == 0x02 || current->acc_status == 0x03) &&
        (previous->acc_status == 0x00 || previous->acc_status == 0x01)) {
        acc_on_time = esp_timer_get_time() / 1000;
        acc_triggered = true;
        return false;  // 不立即触发，等待3秒
    }

    // ACC关闭时重置状态
    if (current->acc_status == 0x00 || current->acc_status == 0x01) {
        acc_on_time = 0;
        acc_triggered = false;
        return false;
    }

    // 检查是否已过3秒延迟
    if (acc_triggered && acc_on_time > 0) {
        uint32_t current_time = esp_timer_get_time() / 1000;
        if (current_time - acc_on_time >= 3000) {  // 3秒延迟
            acc_on_time = 0;
            acc_triggered = false;
            return true;   // 3秒后触发
        }
    }

    return false;
}

static bool check_r_gear_reminder(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 档位变为R档 (360协议: 0x01)
    return (current->gear_position == 0x01 && previous->gear_position != 0x01);
}

static bool check_d_gear_reminder(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 档位变为D档 (360协议: 0x03)
    return (current->gear_position == 0x03 && previous->gear_position != 0x03);
}

static bool check_driver_door_open(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 主驾车门开启：ACC ON + 车速=0 + 主驾门开启（文档要求）
    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);
    return (acc_on && current->vehicle_speed == 0 &&
            current->doors.driver_door && !previous->doors.driver_door);
}

static bool check_passenger_door_open(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 副驾车门开启：ACC ON + 车速=0 + 副驾门开启（文档要求）
    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);
    return (acc_on && current->vehicle_speed == 0 &&
            current->doors.passenger_door && !previous->doors.passenger_door);
}

static bool check_rear_left_door_open(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 左后门开启：ACC ON + 车速=0 + 左后门开启（文档要求）
    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);
    return (acc_on && current->vehicle_speed == 0 &&
            current->doors.rear_left_door && !previous->doors.rear_left_door);
}

static bool check_rear_right_door_open(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 右后门开启：ACC ON + 车速=0 + 右后门开启（文档要求）
    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);
    return (acc_on && current->vehicle_speed == 0 &&
            current->doors.rear_right_door && !previous->doors.rear_right_door);
}

static bool check_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 车速大于5km/h且有车门开启 (通用检查，不应该直接使用)
    bool any_door_open = current->doors.driver_door || current->doors.passenger_door ||
                        current->doors.rear_left_door || current->doors.rear_right_door;
    return (current->vehicle_speed > 5 && any_door_open);
}

static bool check_driver_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 主驾车门未关警告：车速≥10km/h + 主驾门开启，只播报一次直到重置（文档要求）
    static bool warning_triggered = false;

    // 重置条件：车速<10km/h或门关闭
    if (current->vehicle_speed < 10 || !current->doors.driver_door) {
        warning_triggered = false;
        return false;
    }

    // 触发条件：车速≥10km/h且主驾门开启，且未曾触发过
    if (current->vehicle_speed >= 10 && current->doors.driver_door && !warning_triggered) {
        warning_triggered = true;
        return true;
    }

    return false;
}

static bool check_passenger_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 副驾车门未关警告：车速≥10km/h + 副驾门开启，只播报一次直到重置（文档要求）
    static bool warning_triggered = false;

    // 重置条件：车速<10km/h或门关闭
    if (current->vehicle_speed < 10 || !current->doors.passenger_door) {
        warning_triggered = false;
        return false;
    }

    // 触发条件：车速≥10km/h且副驾门开启，且未曾触发过
    if (current->vehicle_speed >= 10 && current->doors.passenger_door && !warning_triggered) {
        warning_triggered = true;
        return true;
    }

    return false;
}

static bool check_rear_left_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 左后门未关警告：车速≥10km/h + 左后门开启，只播报一次直到重置（文档要求）
    static bool warning_triggered = false;

    // 重置条件：车速<10km/h或门关闭
    if (current->vehicle_speed < 10 || !current->doors.rear_left_door) {
        warning_triggered = false;
        return false;
    }

    // 触发条件：车速≥10km/h且左后门开启，且未曾触发过
    if (current->vehicle_speed >= 10 && current->doors.rear_left_door && !warning_triggered) {
        warning_triggered = true;
        return true;
    }

    return false;
}

static bool check_rear_right_door_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 右后门未关警告：车速≥10km/h + 右后门开启，只播报一次直到重置（文档要求）
    static bool warning_triggered = false;

    // 重置条件：车速<10km/h或门关闭
    if (current->vehicle_speed < 10 || !current->doors.rear_right_door) {
        warning_triggered = false;
        return false;
    }

    // 触发条件：车速≥10km/h且右后门开启，且未曾触发过
    if (current->vehicle_speed >= 10 && current->doors.rear_right_door && !warning_triggered) {
        warning_triggered = true;
        return true;
    }

    return false;
}

static bool check_parking_remind(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 停车未熄火提醒：停车≥1小时后开始，每1小时一次，最多3次（文档要求）
    static uint32_t parking_start_time = 0;
    static uint32_t last_remind_time = 0;
    static uint8_t remind_count = 0;

    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);

    if (acc_on && current->vehicle_speed == 0) {
        if (parking_start_time == 0) {
            parking_start_time = current->last_update_time;
        }

        uint32_t parking_duration = current->last_update_time - parking_start_time;
        uint32_t time_since_last = current->last_update_time - last_remind_time;

        // 停车1小时后开始提醒，每1小时一次，最多3次
        if (parking_duration >= 3600000 &&
            time_since_last >= 3600000 &&
            remind_count < 3) {
            last_remind_time = current->last_update_time;
            remind_count++;
            return true;
        }
    } else {
        // 车辆移动或ACC关闭时重置
        parking_start_time = 0;
        last_remind_time = 0;
        remind_count = 0;
    }

    return false;
}

static bool check_steering_warning(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 方向盘预警：车速≥60km/h + 转角≥15° + 转向灯未开 + 持续20秒（文档要求）
    static uint32_t warning_start_time = 0;

    // 计算方向盘偏离中心的角度（360协议：0x80为中心）
    int angle_deviation = abs(current->steering_angle - 128);

    // 检查触发条件
    bool condition_met = (current->vehicle_speed >= 60 &&
                         angle_deviation >= 15 &&
                         !current->turn_signals.left_signal &&
                         !current->turn_signals.right_signal);

    if (condition_met) {
        if (warning_start_time == 0) {
            warning_start_time = esp_timer_get_time() / 1000;
        }

        // 检查是否持续20秒
        uint32_t current_time = esp_timer_get_time() / 1000;
        if (current_time - warning_start_time >= 20000) {  // 20秒持续
            warning_start_time = 0;
            return true;
        }
    } else {
        warning_start_time = 0;  // 条件不满足时重置
    }

    return false;
}

static bool check_fatigue_driving(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 连续驾驶超过2小时
    static uint32_t driving_start_time = 0;

    bool acc_on = (current->acc_status == 0x02 || current->acc_status == 0x03);
    if (acc_on && current->vehicle_speed > 0) {
        if (driving_start_time == 0) {
            driving_start_time = current->last_update_time;
        }
        return (current->last_update_time - driving_start_time > 7200000); // 2小时
    } else {
        driving_start_time = 0;
        return false;
    }
}

static bool check_take_items(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 熄火物品提醒：ACC OFF + P档 + 主驾门从关→开（文档要求）
    bool acc_off = (current->acc_status == 0x00 || current->acc_status == 0x01);
    bool p_gear = (current->gear_position == 0x00);  // P档
    bool driver_door_opened = (current->doors.driver_door && !previous->doors.driver_door);

    return (acc_off && p_gear && driver_door_opened);
}

static bool check_steering_center(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // P档方向盘未回正提醒：ACC从ON→OFF + 转角≥15°（文档要求）
    bool acc_turned_off = ((current->acc_status == 0x00 || current->acc_status == 0x01) &&
                          (previous->acc_status == 0x02 || previous->acc_status == 0x03));

    // 计算方向盘偏离中心的角度（360协议：0x80为中心，文档要求≥15°）
    int angle_deviation = abs(current->steering_angle - 128);

    return (acc_turned_off && angle_deviation >= 15);
}

static bool check_turn_signal_long(const vehicle_state_t* current, const vehicle_state_t* previous) {
    // 转向灯持续过长提醒：D档 + 车速≥30km/h + 转向灯开启≥20秒（文档要求）
    // 20秒后开始，每10秒一次，最多3次，2分钟重置计数
    static uint32_t signal_start_time = 0;
    static uint32_t last_remind_time = 0;
    static uint8_t remind_count = 0;
    static uint32_t count_reset_time = 0;
    static uint32_t delay_start_time = 0;
    static bool in_delay_period = false;

    bool signal_on = (current->turn_signals.left_signal || current->turn_signals.right_signal);
    bool condition_met = (current->gear_position == 0x03 &&  // D档
                         current->vehicle_speed >= 30 &&     // 车速≥30km/h
                         signal_on);                          // 转向灯开启

    if (condition_met) {
        // 30秒延迟期机制：转向灯关闭后30秒内重新开启不重新计时
        if (in_delay_period && (current->last_update_time - delay_start_time < 30000)) {
            return false;  // 延迟期内，不计时
        }

        if (signal_start_time == 0) {
            signal_start_time = current->last_update_time;
            in_delay_period = false;
        }

        uint32_t signal_duration = current->last_update_time - signal_start_time;
        uint32_t time_since_last = current->last_update_time - last_remind_time;

        // 20秒后开始提醒，每10秒一次，最多3次
        if (signal_duration >= 20000 &&
            time_since_last >= 10000 &&
            remind_count < 3) {
            last_remind_time = current->last_update_time;
            remind_count++;
            return true;
        }
    } else {
        if (signal_start_time > 0) {
            // 转向灯关闭，进入30秒延迟期
            delay_start_time = current->last_update_time;
            in_delay_period = true;
        }
        signal_start_time = 0;
    }

    // 每2分钟重置计数
    if (current->last_update_time - count_reset_time >= 120000) {
        remind_count = 0;
        count_reset_time = current->last_update_time;
    }

    return false;
}

// ========== 模式切换功能实现 ==========





// ========== 公共接口实现 ==========

// ========== 指令数据处理 ==========

static bool is_car_data(const uint8_t* data, size_t len) {
    if (len < 2) return false;

    // 检查是否为汽车CAN数据（只接受0x2E开头的二进制数据）
    // 汽车CAN数据特征：
    // 1. 第一个字节必须是0x2E
    // 2. 数据长度通常是12字节（完整的CAN帧）
    // 3. 第二个字节通常是0x03（数据类型）
    if (data[0] == 0x2E) {
        // 进一步验证：检查是否符合CAN数据格式
        if (len >= 4) {
            // 检查第二个字节是否为合理的数据类型（0x03是常见的）
            uint8_t data_type = data[1];
            if (data_type == 0x03 || data_type == 0x01 || data_type == 0x02) {
                return true;
            }
        }
        // 如果长度不够或数据类型不匹配，仍然当作汽车数据处理（兼容性）
        return true;
    }

    // 不接受文本格式的"2E"，只接受二进制0x2E
    return false;
}

static void process_received_data(const uint8_t* data, size_t len) {
    if (!data || len == 0) {
        return;
    }

    if (is_car_data(data, len)) {
        // 汽车CAN数据：使用汽车语音系统处理
        ESP_LOGI(TAG, "🚗 Car CAN data detected: %d bytes", (int)len);
        process_car_voice_data(data, len);
    } else {
        // 指令管理数据：转发给UART指令管理器
        ESP_LOGI(TAG, "📝 Command data detected: %d bytes, forwarding to command manager", (int)len);
        extern void network_console_process_uart_data(const char* data, int len);
        network_console_process_uart_data((const char*)data, (int)len);
    }
}

static void process_car_voice_data(const uint8_t* data, size_t len) {
    // 处理汽车CAN数据（二进制格式）
    ESP_LOGD(TAG, "🚗 Processing car CAN data: %d bytes", (int)len);

    // 逐字节解析汽车数据
    for (int i = 0; i < len; i++) {
        parse_result_t result = car_data_parser_feed(&g_parser, data[i]);

        if (result == PARSE_RESULT_ERROR) {
            ESP_LOGW(TAG, "🚗❌ Parse error at byte %d: 0x%02X", i, data[i]);
        } else if (result == PARSE_RESULT_CHECKSUM_ERROR) {
            ESP_LOGW(TAG, "🚗❌ Checksum error at byte %d: 0x%02X", i, data[i]);
        } else if (result == PARSE_RESULT_INCOMPLETE) {
            ESP_LOGD(TAG, "🚗⏳ Parse incomplete at byte %d: 0x%02X", i, data[i]);
        }

        if (result == PARSE_RESULT_OK && car_data_parser_has_packet(&g_parser)) {
            car_data_packet_t packet = car_data_parser_get_packet(&g_parser);
            ESP_LOGI(TAG, "🚗✅ Valid car data packet received");

            // 打印数据包详细信息
            ESP_LOGI(TAG, "🚗📦 Packet: Header=0x%02X, Type=0x%02X, Len=%d, Checksum=0x%02X",
                    packet.header, packet.data_type, packet.length, packet.checksum);

            // 备份当前状态
            g_previous_state = g_current_state;

            // 更新车辆状态
            if (car_data_update_vehicle_state(&packet, &g_current_state)) {
                g_current_state.last_update_time = esp_timer_get_time() / 1000;
                g_system_status.data_received_count++;
                g_system_status.last_data_time = g_current_state.last_update_time;

                ESP_LOGI(TAG, "🚗📊 Vehicle state updated - Count: %lu",
                        g_system_status.data_received_count);

                // 检查触发条件
                if (check_voice_triggers()) {
                    ESP_LOGI(TAG, "🚗🔊 Voice trigger activated");
                }
            } else {
                ESP_LOGW(TAG, "🚗⚠️ Failed to update vehicle state");
            }
        }
    }
}

// ========== 兼容性接口实现 ==========

esp_err_t car_voice_trigger_get_status(car_voice_trigger_status_t* status) {
    if (!status) {
        return ESP_ERR_INVALID_ARG;
    }

    status->enabled = g_enabled;
    status->voice_enabled = g_voice_enabled;
    status->packets_received = g_system_status.data_received_count;
    status->voice_played_count = g_system_status.voice_played_count;
    status->parse_errors = 0; // 暂时设为0，可以后续添加错误计数
    status->last_update_time = g_system_status.last_data_time;
    status->protocol_type = 1; // 默认协议类型

    // 复制当前车辆状态
    memcpy(&status->vehicle_state, &g_current_state, sizeof(vehicle_state_t));

    return ESP_OK;
}

esp_err_t car_voice_trigger_play_voice(uint8_t voice_id, uint8_t priority) {
    if (!g_initialized) {
        ESP_LOGE(TAG, "Car voice trigger not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (voice_id < MIN_VOICE_ID || voice_id > MAX_VOICE_ID) {
        ESP_LOGE(TAG, "Invalid voice ID: %d", voice_id);
        return ESP_ERR_INVALID_ARG;
    }

    return queue_voice_play(voice_id, priority);
}

esp_err_t car_voice_trigger_inject_data(const uint8_t* data, size_t len) {
    if (!data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!g_initialized) {
        ESP_LOGE(TAG, "Car voice trigger not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 将数据发送到UART队列进行处理
    uart_event_t event = {
        .type = UART_DATA,
        .size = len
    };

    if (xQueueSend(g_car_uart_queue, &event, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGW(TAG, "Failed to inject data to queue");
        return ESP_ERR_TIMEOUT;
    }

    ESP_LOGI(TAG, "Injected %zu bytes of data", len);
    return ESP_OK;
}

esp_err_t car_voice_trigger_enable(bool enabled) {
    if (!g_initialized) {
        ESP_LOGE(TAG, "Car voice trigger not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    g_enabled = enabled;
    g_uart_enabled = enabled;

    ESP_LOGI(TAG, "Car voice trigger %s", enabled ? "enabled" : "disabled");
    return ESP_OK;
}
