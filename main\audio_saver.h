#ifndef AUDIO_SAVER_H
#define AUDIO_SAVER_H

#include <vector>
#include <string>
#include <mutex>
#include "protocols/protocol.h"

/**
 * 音频保存器 - 最小侵入式设计
 * 用于保存小智接收到的TTS音频数据为P3格式
 */
class AudioSaver {
public:
    AudioSaver();
    ~AudioSaver();

    /**
     * 启用/禁用音频保存功能
     */
    void SetEnabled(bool enabled);
    bool IsEnabled() const;

    /**
     * 开始新的音频保存会话
     */
    void StartSession();

    /**
     * 保存音频数据包
     * @param packet 音频数据包
     */
    void SaveAudioPacket(const AudioStreamPacket& packet);

    /**
     * 结束当前音频保存会话
     * @return 保存的文件路径，失败返回空字符串
     */
    std::string EndSession();

    /**
     * 获取状态信息
     */
    struct Status {
        bool enabled;
        bool session_active;
        int current_packets;
        int file_counter;
        std::string last_saved_file;
    };
    Status GetStatus() const;

    /**
     * 重置文件计数器
     */
    void ResetCounter();

private:
    bool enabled_;
    bool session_active_;
    std::vector<uint8_t> current_audio_data_;
    int file_counter_;
    std::string last_saved_file_;
    mutable std::mutex mutex_;

    /**
     * 生成文件名
     */
    std::string GenerateFileName();

    /**
     * 保存数据到文件
     */
    bool SaveToFile(const std::string& filename, const std::vector<uint8_t>& data);
};

// 全局实例访问函数
AudioSaver& GetAudioSaver();

// 安全的对外接口函数
void SafeStartAudioSession();
void SafeSaveAudioPacket(const AudioStreamPacket& packet);
std::string SafeEndAudioSession();

#endif // AUDIO_SAVER_H
