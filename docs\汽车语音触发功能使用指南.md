# 汽车语音触发功能使用指南

## 📋 功能概述

汽车语音触发功能是小智ESP32的扩展模块，通过UART2接口接收汽车数据，根据车辆状态变化自动触发语音播报。

### 🎯 主要特性
- **智能数据解析**：支持360协议和自定义协议
- **状态变化检测**：实时监控车辆状态变化
- **语音自动播报**：根据预设条件触发语音播放
- **互斥设计**：与UART指令管理器互斥，确保系统稳定
- **可配置开关**：通过Kconfig配置启用/禁用

## 🔧 启用汽车语音触发功能

### 1. 配置编译选项

使用ESP-IDF配置工具启用功能：

```bash
cd J:\xiaozhi-esp32
idf.py menuconfig
```

在配置菜单中：
1. 进入 `Xiaozhi Assistant` 菜单
2. 启用 `Enable Car Voice Trigger`
3. 配置相关参数：
   - **Car UART Baud Rate**: 设置波特率（默认19200）
   - **Car Protocol Type**: 选择协议类型（默认自动检测）
   - **Enable Car Voice Debug**: 启用调试日志（推荐）
   - **Enable Car Voice Test Mode**: 启用测试模式（可选）

### 2. 重新编译和烧录

```bash
# 清理并重新编译
idf.py clean
idf.py build

# 烧录到ESP32
idf.py flash monitor
```

## 🔌 硬件连接

### UART2接口配置
- **TX引脚**: GPIO17
- **RX引脚**: GPIO16
- **波特率**: 19200（可配置）
- **数据位**: 8位
- **停止位**: 1位
- **奇偶校验**: 无

### 连接示例
```
汽车ECU/OBD接口    →    ESP32-S3
    TX             →    GPIO16 (RX)
    RX             →    GPIO17 (TX)
    GND            →    GND
    VCC            →    3.3V/5V
```

## 📊 支持的数据协议

### 1. 360协议
标准的汽车数据协议，包含以下数据类型：
- **状态数据**: ACC状态、档位、车门、转向灯等
- **速度数据**: 车辆速度信息
- **车门数据**: 四门开关状态
- **档位数据**: 当前档位信息
- **转向灯数据**: 左右转向灯状态
- **方向盘数据**: 方向盘转角

### 2. 自定义协议
简化的数据协议，所有状态信息在一个数据包中：
```
字节0: ACC状态
字节1: 档位
字节2-3: 车速（高字节在前）
字节4: 车门状态（位0-3分别对应四门）
字节5: 转向灯状态（位0-1分别对应左右转向灯）
字节6: 方向盘转角
```

### 3. 数据包格式
```
[包头] [数据类型] [数据长度] [数据内容] [校验和]
```

## 🎵 语音文件配置

### 语音文件存储
语音文件存储在ESP32的音频分区中：
```
/audio/
├── 001.p3  - 欢迎语音
├── 002.p3  - 车门开启提醒
├── 003.p3  - 车门关闭提醒
├── 004.p3  - 左转向灯提醒
├── 005.p3  - 右转向灯提醒
├── 006.p3  - 倒车提醒
├── 007.p3  - 超速提醒
└── ...
```

### 语音ID映射
```c
typedef enum {
    VOICE_ID_WELCOME = 1,           // 001.p3 - 欢迎使用
    VOICE_ID_DOOR_OPEN = 2,         // 002.p3 - 车门开启
    VOICE_ID_DOOR_CLOSE = 3,        // 003.p3 - 车门关闭
    VOICE_ID_LEFT_TURN = 4,         // 004.p3 - 左转向灯
    VOICE_ID_RIGHT_TURN = 5,        // 005.p3 - 右转向灯
    VOICE_ID_REVERSE = 6,           // 006.p3 - 倒车提醒
    VOICE_ID_OVERSPEED = 7,         // 007.p3 - 超速提醒
    // 可根据需要添加更多语音
} car_voice_id_t;
```

## 🧪 测试和调试

### 1. 启用调试模式
在menuconfig中启用 `Enable Car Voice Debug`，可以看到详细的调试信息：

```
I (12345) CarVoiceTrigger: Car data received: header=0xAA, type=0x01, len=8
I (12346) CarDataParser: Parsing 360 protocol data
I (12347) CarVoiceTrigger: Door state changed: driver_door=1
I (12348) CarVoiceTrigger: 🚗🔊 Playing car voice: /audio/002.p3 (priority: 2)
```

### 2. 测试模式
启用 `Enable Car Voice Test Mode` 后，可以通过串口命令测试：

```bash
# 查看汽车语音触发状态
xiaozhi> car_status

# 手动播放语音
xiaozhi> car_play 2

# 注入测试数据
xiaozhi> car_inject AA010800010203040506
```

### 3. 监控系统状态
```bash
# 查看系统状态
xiaozhi> textstatus

# 查看内存使用
xiaozhi> heap

# 查看任务状态
xiaozhi> tasks
```

## ⚠️ 注意事项

### 1. 功能互斥
- 汽车语音触发功能与UART指令管理器互斥
- 启用汽车功能后，UART指令管理器将被禁用
- 如需使用UART指令功能，请禁用汽车语音触发

### 2. 硬件要求
- 需要ESP32-S3芯片
- 确保UART2引脚未被其他功能占用
- 汽车数据源需要提供稳定的串口信号

### 3. 性能考虑
- 汽车数据解析在独立任务中运行，不影响主功能
- 语音播放使用现有音频系统，与小智语音共享
- 建议监控内存使用，确保系统稳定

### 4. 安全提醒
- 请确保汽车数据连接的安全性
- 避免在驾驶过程中进行调试操作
- 语音提醒不应干扰正常驾驶

## 🔧 故障排除

### 常见问题

#### 1. 编译错误
```
error: 'car_voice_trigger_init' was not declared
```
**解决方案**: 确保在menuconfig中启用了汽车语音触发功能

#### 2. 无法接收数据
```
W (12345) CarVoiceTrigger: No data received from car
```
**解决方案**: 
- 检查UART连接
- 确认波特率设置
- 检查汽车数据源

#### 3. 语音播放失败
```
E (12345) CarVoiceTrigger: Failed to play car voice: /audio/002.p3
```
**解决方案**:
- 确认语音文件存在
- 检查音频分区挂载
- 查看音频系统状态

#### 4. 内存不足
```
E (12345) CarVoiceTrigger: Failed to allocate memory
```
**解决方案**:
- 监控内存使用
- 优化缓冲区大小
- 检查内存泄漏

## 📈 性能优化建议

1. **数据处理优化**
   - 合理设置接收缓冲区大小
   - 避免频繁的内存分配
   - 使用高效的数据解析算法

2. **语音播放优化**
   - 预加载常用语音文件
   - 设置合理的播放优先级
   - 避免语音播放冲突

3. **系统资源管理**
   - 监控任务栈使用
   - 合理设置任务优先级
   - 定期检查系统健康状态

## 📞 技术支持

如果遇到问题，请：
1. 查看串口日志获取详细错误信息
2. 确认硬件连接和配置
3. 检查语音文件和音频分区状态
4. 监控系统资源使用情况

---

**版本**: 1.0.0  
**更新日期**: 2025-07-29  
**兼容性**: ESP32-S3, ESP-IDF v5.4.1+
