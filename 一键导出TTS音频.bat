@echo off
chcp 65001 >nul
title <PERSON><PERSON> TTS Audio Export Tool

echo.
echo ========================================
echo   Xiaozhi TTS Audio Export Tool
echo ========================================
echo.

cd /d J:\xiaozhi-esp32

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not available, trying ESP-IDF environment...
    set PYTHON_CMD=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe
) else (
    set PYTHON_CMD=python
)

echo Checking export script...
if not exist "receive_tts_export.py" (
    echo ERROR: Cannot find receive_tts_export.py script
    echo Please ensure the script is in J:\xiaozhi-esp32 directory
    pause
    exit /b 1
)

echo.
echo Starting TTS audio export tool...
echo Please ensure ESP32 is connected and running normally
echo.

%PYTHON_CMD% receive_tts_export.py

echo.
echo Export completed! Files saved to:
echo    J:\xiaozhi-esp32\audio_files\
echo.

pause
