#include "network_console.h"
#include "settings.h"
#include "board.h"
#include "boards/common/dual_network_board.h"
#include "application.h"
#include "text_interaction/text_commands.h"
#include "audio_saver.h"
#include "communication/uart_command_manager.h"
// #include "command_config/command_config_integration.h"
#include <dirent.h>
#include <sys/stat.h>
#include <unistd.h>
#include <vector>
#include <algorithm>

#include <esp_console.h>
#include <esp_log.h>
#include <esp_mac.h>
#include <esp_chip_info.h>
#include <esp_ota_ops.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <dirent.h>
#include <string.h>
#include <vector>
#include <string_view>

#define TAG "NetworkConsole"

NetworkConsole& NetworkConsole::GetInstance() {
    static NetworkConsole instance;
    return instance;
}

void NetworkConsole::Initialize() {
    if (initialized_) {
        return;
    }

    esp_console_repl_t *repl = NULL;
    esp_console_repl_config_t repl_config = ESP_CONSOLE_REPL_CONFIG_DEFAULT();
    repl_config.max_cmdline_length = 1024;
    repl_config.prompt = "xiaozhi>";

    RegisterNetworkCommands();

    // 初始化文本交互命令
    auto& app = Application::GetInstance();
    auto text_manager = app.GetTextInteractionManager();
    if (text_manager) {
        if (text_interaction::TextCommands::Initialize(text_manager)) {
            text_interaction::TextCommands::RegisterCommands();
            ESP_LOGI(TAG, "Text interaction commands initialized successfully");
        } else {
            ESP_LOGE(TAG, "Failed to initialize text interaction commands");
        }
    } else {
        ESP_LOGW(TAG, "Text interaction manager not available");
    }

    // 初始化UART指令管理器
    InitializeUartCommandManager();

    // 初始化指令配置系统 - 暂时注释掉
    /*
    command_config::ConfigResult config_result = command_config::InitializeCommandConfigSystem(true);
    if (config_result == command_config::CONFIG_OK) {
        ESP_LOGI(TAG, "Command config system initialized successfully");
        command_config::PrintCommandConfigInfo();
    } else {
        ESP_LOGE(TAG, "Failed to initialize command config system: %s",
                 command_config::CommandConfigManager::GetErrorString(config_result));
    }
    */

    esp_console_dev_uart_config_t hw_config = ESP_CONSOLE_DEV_UART_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_uart(&hw_config, &repl_config, &repl));
    ESP_ERROR_CHECK(esp_console_start_repl(repl));

    initialized_ = true;
    ESP_LOGI(TAG, "Network console initialized. Type 'nethelp' or 'texthelp' for available commands.");
}

void NetworkConsole::RegisterNetworkCommands() {
    // 4G命令
    const esp_console_cmd_t cmd_4g = {
        .command = "4g",
        .help = "Switch to 4G network (ML307)",
        .hint = nullptr,
        .func = CmdSwitchTo4G,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_4g));

    // WiFi命令
    const esp_console_cmd_t cmd_wifi = {
        .command = "wifi",
        .help = "Switch to WiFi network",
        .hint = nullptr,
        .func = CmdSwitchToWiFi,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_wifi));

    // 网络状态命令
    const esp_console_cmd_t cmd_status = {
        .command = "status",
        .help = "Show current network status",
        .hint = nullptr,
        .func = CmdNetworkStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_status));

    // 帮助命令
    const esp_console_cmd_t cmd_help_net = {
        .command = "nethelp",
        .help = "Show network commands help",
        .hint = nullptr,
        .func = CmdHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_help_net));

    // 重启命令
    const esp_console_cmd_t cmd_reboot = {
        .command = "reboot",
        .help = "Reboot the device",
        .hint = nullptr,
        .func = CmdReboot,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_reboot));

    // 本地音频播放命令
    const esp_console_cmd_t cmd_local = {
        .command = "local",
        .help = "Play local audio files",
        .hint = "[file_number]",
        .func = CmdLocalAudio,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_local));

    // 音频保存状态命令
    const esp_console_cmd_t cmd_audio_status = {
        .command = "audio_status",
        .help = "Show audio saver status",
        .hint = nullptr,
        .func = CmdAudioStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_audio_status));

    // 音频保存控制命令
    const esp_console_cmd_t cmd_audio_save = {
        .command = "audio_save",
        .help = "Control audio saving (on/off)",
        .hint = "[on|off]",
        .func = CmdAudioSave,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_audio_save));

    // TTS音频列表命令
    const esp_console_cmd_t cmd_tts_list = {
        .command = "tts_list",
        .help = "List all saved TTS audio files",
        .hint = nullptr,
        .func = CmdTtsList,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_list));

    // TTS音频播放命令
    const esp_console_cmd_t cmd_tts_play = {
        .command = "tts_play",
        .help = "Play saved TTS audio file",
        .hint = "<number>",
        .func = CmdTtsPlay,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_play));

    // TTS音频清理命令
    const esp_console_cmd_t cmd_tts_clear = {
        .command = "tts_clear",
        .help = "Delete all saved TTS audio files",
        .hint = nullptr,
        .func = CmdTtsClear,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_clear));

    // TTS音频导出命令
    const esp_console_cmd_t cmd_tts_export = {
        .command = "tts_export",
        .help = "Export TTS audio files info for PC download",
        .hint = nullptr,
        .func = CmdTtsExport,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_export));

    // TTS音频十六进制导出命令
    const esp_console_cmd_t cmd_tts_export_hex = {
        .command = "tts_export_hex",
        .help = "Export TTS audio file as hex data",
        .hint = "<filename>",
        .func = CmdTtsExportHex,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_export_hex));

    // TTS音频批量导出命令
    const esp_console_cmd_t cmd_tts_export_all = {
        .command = "tts_export_all",
        .help = "Export all TTS audio files to PC",
        .hint = nullptr,
        .func = CmdTtsExportAll,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_tts_export_all));

    // ========== 指令配置管理命令 ==========
    // 暂时注释掉，等待函数定义完成后再启用
    /*
    // 配置状态查询命令
    const esp_console_cmd_t cmd_config_status = {
        .command = "config_status",
        .help = "Show command configuration system status",
        .hint = nullptr,
        .func = CmdConfigStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_config_status));

    // 配置列表命令
    const esp_console_cmd_t cmd_config_list = {
        .command = "config_list",
        .help = "List all command configurations",
        .hint = nullptr,
        .func = CmdConfigList,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_config_list));

    // 添加配置命令
    const esp_console_cmd_t cmd_config_add = {
        .command = "config_add",
        .help = "Add new command configuration",
        .hint = "<command> <text> [description]",
        .func = CmdConfigAdd,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_config_add));

    // 修改配置命令
    const esp_console_cmd_t cmd_config_modify = {
        .command = "config_modify",
        .help = "Modify command text configuration",
        .hint = "<command> <new_text>",
        .func = CmdConfigModify,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_config_modify));

    // 保存配置命令
    const esp_console_cmd_t cmd_config_save = {
        .command = "config_save",
        .help = "Save all configurations to storage",
        .hint = nullptr,
        .func = CmdConfigSave,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_config_save));
    */


}

int NetworkConsole::CmdSwitchTo4G(int argc, char** argv) {
    printf("Switching to 4G network (ML307)...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    // 通过板卡类型字符串检查是否支持双网络
    std::string board_type = board.GetBoardType();
    if (board_type != "wifi" && board_type != "ml307") {
        printf("❌ Error: This device does not support dual network mode.\n");
        printf("   4G network is not available on this device.\n");
        printf("   Available commands: wifi, status, nethelp\n");
        return 1;
    }

    // 对于支持双网络的板卡，使用static_cast（我们已经通过类型检查确认了）
    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::ML307) {
        printf("✅ Already using 4G network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 1); // 1 = ML307 (4G)

    printf("Network type set to 4G. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

int NetworkConsole::CmdNetworkStatus(int argc, char** argv) {
    printf("\n=== Network Status ===\n");

    // 获取设备信息
    auto& board = Board::GetInstance();

    // 读取当前网络设置
    Settings settings("network", false);
    int network_type = settings.GetInt("type", 1); // 默认ML307

    // 通过板卡类型检查是否支持双网络
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        auto* dual_board = static_cast<DualNetworkBoard*>(&board);
        printf("Device Type: Dual Network (WiFi + 4G)\n");

        NetworkType current_type = dual_board->GetNetworkType();
        if (current_type == NetworkType::ML307) {
            printf("Current Network: 4G (ML307)\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        } else {
            printf("Current Network: WiFi\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        }
        printf("Supported Networks: WiFi, 4G (ML307)\n");
        printf("Network Switching: ✅ Available\n");
    } else {
        printf("Device Type: WiFi Only\n");
        printf("Current Network: WiFi (Only)\n");
        printf("Supported Networks: WiFi only\n");
        printf("Network Switching: ❌ Not available\n");
        if (network_type == 1) {
            printf("⚠️  Warning: 4G setting detected but device only supports WiFi\n");
        }
    }

    // 显示设备信息
    printf("\n=== Device Info ===\n");

    // MAC地址
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    printf("WiFi MAC: " MACSTR "\n", MAC2STR(mac));

    // 芯片信息
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    printf("Chip: %s Rev %d\n",
           chip_info.model == CHIP_ESP32 ? "ESP32" :
           chip_info.model == CHIP_ESP32S2 ? "ESP32-S2" :
           chip_info.model == CHIP_ESP32S3 ? "ESP32-S3" :
           chip_info.model == CHIP_ESP32C3 ? "ESP32-C3" : "Unknown",
           chip_info.revision);

    // 固件版本
    const esp_app_desc_t* app_desc = esp_ota_get_app_description();
    printf("Firmware: %s\n", app_desc->version);
    printf("Build Date: %s %s\n", app_desc->date, app_desc->time);

    printf("==================\n\n");

    return 0;
}

int NetworkConsole::CmdHelp(int argc, char** argv) {
    printf("\n=== Network Console Commands ===\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        printf("4g        - Switch to 4G network (ML307)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("local     - Play local audio files\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: Dual Network Board\n");
        printf("  Networks: WiFi + 4G (ML307)\n");
        printf("  Switching: ✅ Available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> 4g       # Switch to 4G\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
        printf("  xiaozhi> local    # List audio files\n");
        printf("  xiaozhi> local 001 # Play audio file 001\n");
    } else {
        printf("4g        - ❌ Not available (WiFi-only device)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("local     - Play local audio files\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: WiFi Only Board\n");
        printf("  Networks: WiFi only\n");
        printf("  Switching: ❌ Not available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
        printf("  xiaozhi> local    # List audio files\n");
        printf("  xiaozhi> local 001 # Play audio file 001\n");
    }
    printf("================================\n\n");

    return 0;
}

int NetworkConsole::CmdReboot(int argc, char** argv) {
    printf("Rebooting device in 3 seconds...\n");
    for (int i = 3; i > 0; i--) {
        printf("%d...\n", i);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    printf("Rebooting now!\n");
    esp_restart();
    return 0;
}

int NetworkConsole::CmdSwitchToWiFi(int argc, char** argv) {
    printf("Switching to WiFi network...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (!is_dual_network) {
        printf("✅ This device only supports WiFi network.\n");
        return 0;
    }

    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::WIFI) {
        printf("✅ Already using WiFi network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 0); // 0 = WiFi

    printf("Network type set to WiFi. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

int NetworkConsole::CmdLocalAudio(int argc, char** argv) {
    if (argc == 1) {
        // 显示可用的音频文件列表
        printf("\n=== Local Audio Files ===\n");

        DIR* dir = opendir("/audio");
        if (dir == nullptr) {
            printf("❌ Failed to open /audio directory\n");
            return 1;
        }

        struct dirent* entry;
        int file_count = 0;

        while ((entry = readdir(dir)) != nullptr) {
            // 检查是否是音频文件（.p3, .aaf格式）
            const char* name = entry->d_name;
            size_t len = strlen(name);

            if (len >= 7) { // 至少 "001.p3" 长度
                // 检查文件名格式：3位数字 + 扩展名
                if (isdigit(name[0]) && isdigit(name[1]) && isdigit(name[2]) && name[3] == '.') {
                    if (strstr(name, ".p3") || strstr(name, ".aaf")) {
                        printf("  %c%c%c - %s\n", name[0], name[1], name[2], name);
                        file_count++;
                    }
                }
            }
        }

        closedir(dir);

        if (file_count == 0) {
            printf("No audio files found in /audio\n");
            printf("\nTo add audio files:\n");
            printf("1. Convert MP3 to P3: python scripts/p3_tools/convert_audio_to_p3.py input.mp3 001.p3\n");
            printf("2. Place files in spiffs_image/ directory\n");
            printf("3. Rebuild and flash firmware\n");
        } else {
            printf("\nFound %d audio files\n", file_count);
            printf("\nUsage: local <number>  (e.g., local 001)\n");
        }

        printf("========================\n\n");
        return 0;
    }

    if (argc == 2) {
        // 播放指定的音频文件
        const char* file_number = argv[1];

        // 验证输入格式（应该是3位数字）
        if (strlen(file_number) != 3 || !isdigit(file_number[0]) ||
            !isdigit(file_number[1]) || !isdigit(file_number[2])) {
            printf("❌ Invalid file number. Use 3-digit format (e.g., 001, 002)\n");
            return 1;
        }

        // 尝试播放P3文件
        char p3_filename[64];
        snprintf(p3_filename, sizeof(p3_filename), "/audio/%s.p3", file_number);

        printf("🎵 Attempting to play: %s\n", p3_filename);

        // 读取P3文件内容
        FILE* file = fopen(p3_filename, "rb");
        if (file != nullptr) {
            // 获取文件大小
            fseek(file, 0, SEEK_END);
            long file_size = ftell(file);
            fseek(file, 0, SEEK_SET);

            if (file_size > 0 && file_size < 1024 * 1024) { // 限制文件大小 < 1MB
                // 读取文件内容
                std::vector<char> file_data(file_size);
                size_t bytes_read = fread(file_data.data(), 1, file_size, file);
                fclose(file);

                if (bytes_read == file_size) {
                    // 播放前检查音频状态
                    auto& app = Application::GetInstance();
                    auto codec = Board::GetInstance().GetAudioCodec();

                    printf("🔊 Audio status before playback:\n");
                    printf("   - Output enabled: %s\n", codec->output_enabled() ? "true" : "false");
                    printf("   - Device state: %d\n", (int)app.GetDeviceState());

                    // 创建string_view并播放
                    std::string_view sound_data(file_data.data(), file_size);
                    app.PlaySound(sound_data);

                    printf("✅ Playing: %s (%ld bytes)\n", p3_filename, file_size);

                    // 播放后再次检查状态
                    printf("🔊 Audio status after queueing:\n");
                    printf("   - Output enabled: %s\n", codec->output_enabled() ? "true" : "false");

                    return 0;
                } else {
                    printf("❌ Failed to read file: %s\n", p3_filename);
                }
            } else {
                fclose(file);
                printf("❌ Invalid file size: %ld bytes\n", file_size);
            }
        }

        // 如果P3文件播放失败，尝试AAF文件
        char aaf_filename[64];
        snprintf(aaf_filename, sizeof(aaf_filename), "/audio/%s.aaf", file_number);

        printf("🎵 Trying AAF format: %s\n", aaf_filename);

        file = fopen(aaf_filename, "rb");
        if (file != nullptr) {
            // 获取文件大小
            fseek(file, 0, SEEK_END);
            long file_size = ftell(file);
            fseek(file, 0, SEEK_SET);

            if (file_size > 0 && file_size < 1024 * 1024) { // 限制文件大小 < 1MB
                // 读取文件内容
                std::vector<char> file_data(file_size);
                size_t bytes_read = fread(file_data.data(), 1, file_size, file);
                fclose(file);

                if (bytes_read == file_size) {
                    // 播放前检查音频状态
                    auto& app = Application::GetInstance();
                    auto codec = Board::GetInstance().GetAudioCodec();

                    printf("🔊 Audio status before playback:\n");
                    printf("   - Output enabled: %s\n", codec->output_enabled() ? "true" : "false");
                    printf("   - Device state: %d\n", (int)app.GetDeviceState());

                    // 创建string_view并播放
                    std::string_view sound_data(file_data.data(), file_size);
                    app.PlaySound(sound_data);

                    printf("✅ Playing: %s (%ld bytes)\n", aaf_filename, file_size);

                    // 播放后再次检查状态
                    printf("🔊 Audio status after queueing:\n");
                    printf("   - Output enabled: %s\n", codec->output_enabled() ? "true" : "false");

                    return 0;
                } else {
                    printf("❌ Failed to read file: %s\n", aaf_filename);
                }
            } else {
                fclose(file);
                printf("❌ Invalid file size: %ld bytes\n", file_size);
            }
        }

        printf("❌ Failed to play audio file %s (tried both .p3 and .aaf)\n", file_number);
        printf("Make sure the file exists in /audio directory\n");
        return 1;
    }

    printf("Usage:\n");
    printf("  local          - List available audio files\n");
    printf("  local <number> - Play audio file (e.g., local 001)\n");
    return 1;
}

int NetworkConsole::CmdAudioStatus(int argc, char** argv) {
    printf("\n=== Audio Saver Status ===\n");

    auto status = GetAudioSaver().GetStatus();

    printf("Enabled: %s\n", status.enabled ? "✅ Yes" : "❌ No");
    printf("Session Active: %s\n", status.session_active ? "🎤 Recording" : "⏹️  Idle");
    printf("Current Packets: %d\n", status.current_packets);
    printf("File Counter: %d\n", status.file_counter);

    if (!status.last_saved_file.empty()) {
        printf("Last Saved: %s\n", status.last_saved_file.c_str());
    } else {
        printf("Last Saved: None\n");
    }

    printf("\nSaved files location: /spiffs/tts_*.p3\n");
    printf("Use 'local' command to play saved files\n");

    return 0;
}

int NetworkConsole::CmdAudioSave(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: audio_save [on|off]\n");
        printf("Current status: %s\n", GetAudioSaver().IsEnabled() ? "ON" : "OFF");
        return 1;
    }

    std::string command = argv[1];

    if (command == "on") {
        GetAudioSaver().SetEnabled(true);
        printf("✅ Audio saving enabled\n");
        printf("TTS audio will be automatically saved to /spiffs/tts_*.p3\n");
    } else if (command == "off") {
        GetAudioSaver().SetEnabled(false);
        printf("❌ Audio saving disabled\n");
    } else {
        printf("❌ Invalid option: %s\n", command.c_str());
        printf("Usage: audio_save [on|off]\n");
        return 1;
    }

    return 0;
}

int NetworkConsole::CmdTtsList(int argc, char** argv) {
    printf("\n=== Saved TTS Audio Files ===\n");

    DIR* dir = opendir("/audio");
    if (!dir) {
        printf("❌ Cannot open /audio directory\n");
        return 1;
    }

    struct dirent* entry;
    int count = 0;
    long total_size = 0;

    while ((entry = readdir(dir)) != nullptr) {
        if (strstr(entry->d_name, "tts_") && strstr(entry->d_name, ".p3")) {
            std::string full_path = "/audio/" + std::string(entry->d_name);

            struct stat file_stat;
            if (stat(full_path.c_str(), &file_stat) == 0) {
                count++;
                total_size += file_stat.st_size;

                printf("%d. %s (%ld bytes)\n", count, entry->d_name, file_stat.st_size);
            }
        }
    }
    closedir(dir);

    if (count == 0) {
        printf("No TTS audio files found.\n");
    } else {
        printf("\nTotal: %d files, %ld bytes (%.1f KB)\n",
               count, total_size, total_size / 1024.0);
        printf("\nUsage:\n");
        printf("  tts_play <number> - Play TTS file\n");
        printf("  tts_export        - Export files for PC\n");
        printf("  tts_export_all    - Batch export all files to PC\n");
        printf("  tts_clear         - Delete all TTS files\n");
    }

    return 0;
}

int NetworkConsole::CmdTtsPlay(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: tts_play <number>\n");
        printf("Use 'tts_list' to see available files\n");
        return 1;
    }

    int file_number = atoi(argv[1]);
    if (file_number < 1) {
        printf("❌ Invalid file number: %s\n", argv[1]);
        return 1;
    }

    // 构建文件名
    char filename[32];
    snprintf(filename, sizeof(filename), "tts_%03d.p3", file_number);
    std::string full_path = "/audio/" + std::string(filename);

    // 检查文件是否存在
    struct stat file_stat;
    if (stat(full_path.c_str(), &file_stat) != 0) {
        printf("❌ TTS file not found: %s\n", filename);
        printf("Use 'tts_list' to see available files\n");
        return 1;
    }

    printf("🎵 Playing TTS audio: %s (%ld bytes)\n", filename, file_stat.st_size);

    // 读取文件内容
    FILE* file = fopen(full_path.c_str(), "rb");
    if (!file) {
        printf("❌ Cannot open file for reading: %s\n", filename);
        return 1;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 读取文件内容到字符串
    std::string audio_data(file_size, '\0');
    size_t read_size = fread(audio_data.data(), 1, file_size, file);
    fclose(file);

    if (read_size != file_size) {
        printf("❌ Failed to read complete file: read %zu/%ld bytes\n", read_size, file_size);
        return 1;
    }

    printf("✅ File loaded: %ld bytes\n", file_size);

    // 调用Application的PlaySound方法（传入文件内容）
    try {
        Application::GetInstance().PlaySound(audio_data);
        printf("🎵 Playback started successfully\n");
    } catch (const std::exception& e) {
        printf("❌ Playback failed: %s\n", e.what());
        return 1;
    }

    return 0;
}

int NetworkConsole::CmdTtsClear(int argc, char** argv) {
    printf("🗑️  Clearing all TTS audio files...\n");

    DIR* dir = opendir("/audio");
    if (!dir) {
        printf("❌ Cannot open /audio directory\n");
        return 1;
    }

    struct dirent* entry;
    int deleted_count = 0;
    long freed_bytes = 0;

    while ((entry = readdir(dir)) != nullptr) {
        if (strstr(entry->d_name, "tts_") && strstr(entry->d_name, ".p3")) {
            std::string full_path = "/audio/" + std::string(entry->d_name);

            struct stat file_stat;
            if (stat(full_path.c_str(), &file_stat) == 0) {
                freed_bytes += file_stat.st_size;
            }

            if (unlink(full_path.c_str()) == 0) {
                deleted_count++;
                printf("  ✅ Deleted: %s\n", entry->d_name);
            } else {
                printf("  ❌ Failed to delete: %s\n", entry->d_name);
            }
        }
    }
    closedir(dir);

    printf("\n🎉 Cleanup complete!\n");
    printf("Deleted: %d files\n", deleted_count);
    printf("Freed: %ld bytes (%.1f KB)\n", freed_bytes, freed_bytes / 1024.0);

    // 重置音频保存器的计数器
    GetAudioSaver().ResetCounter();

    return 0;
}

int NetworkConsole::CmdTtsExport(int argc, char** argv) {
    printf("\n=== TTS Audio Export Information ===\n");
    printf("Target directory: J:\\xiaozhi-esp32\\audio_files\\\n\n");

    DIR* dir = opendir("/audio");
    if (!dir) {
        printf("❌ Cannot open /audio directory\n");
        return 1;
    }

    struct dirent* entry;
    int count = 0;

    printf("📋 Files to export:\n");
    while ((entry = readdir(dir)) != nullptr) {
        if (strstr(entry->d_name, "tts_") && strstr(entry->d_name, ".p3")) {
            std::string full_path = "/audio/" + std::string(entry->d_name);

            struct stat file_stat;
            if (stat(full_path.c_str(), &file_stat) == 0) {
                count++;
                printf("%d. %s (%ld bytes)\n", count, entry->d_name, file_stat.st_size);
            }
        }
    }
    closedir(dir);

    if (count == 0) {
        printf("No TTS files to export.\n");
        return 0;
    }

    printf("\n💡 Export Instructions:\n");
    printf("1. Create directory: mkdir J:\\xiaozhi-esp32\\audio_files\n");
    printf("2. Use ESP32 file transfer tool or:\n");
    printf("3. Copy files manually using partition tool\n");
    printf("4. Or use the following Python script:\n\n");

    printf("--- Python Export Script ---\n");
    printf("# Save this as 'export_tts.py' and run on PC\n");
    printf("import serial\n");
    printf("import os\n");
    printf("import time\n\n");
    printf("def export_tts_files():\n");
    printf("    # Connect to ESP32 serial port\n");
    printf("    ser = serial.Serial('COM13', 115200, timeout=1)\n");
    printf("    os.makedirs('J:/xiaozhi-esp32/audio_files', exist_ok=True)\n");
    printf("    \n");
    printf("    # Export each file\n");

    // 重新遍历文件生成导出脚本
    dir = opendir("/audio");
    if (dir) {
        count = 0;
        while ((entry = readdir(dir)) != nullptr) {
            if (strstr(entry->d_name, "tts_") && strstr(entry->d_name, ".p3")) {
                count++;
                printf("    # Export %s\n", entry->d_name);
                printf("    ser.write(b'tts_export_hex %s\\n')\n", entry->d_name);
                printf("    time.sleep(1)\n");
            }
        }
        closedir(dir);
    }

    printf("\nif __name__ == '__main__':\n");
    printf("    export_tts_files()\n");
    printf("--- End of Script ---\n\n");

    printf("🔧 Alternative: Use 'tts_play <number>' to test files first\n");

    return 0;
}

int NetworkConsole::CmdTtsExportHex(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: tts_export_hex <filename>\n");
        printf("Example: tts_export_hex tts_001.p3\n");
        return 1;
    }

    std::string filename = argv[1];
    std::string full_path = "/audio/" + filename;

    // 检查文件是否存在
    struct stat file_stat;
    if (stat(full_path.c_str(), &file_stat) != 0) {
        printf("❌ File not found: %s\n", filename.c_str());
        return 1;
    }

    // 打开文件
    FILE* file = fopen(full_path.c_str(), "rb");
    if (!file) {
        printf("❌ Cannot open file: %s\n", filename.c_str());
        return 1;
    }

    printf("\n=== TTS Audio File Export ===\n");
    printf("File: %s\n", filename.c_str());
    printf("Size: %ld bytes\n", file_stat.st_size);
    printf("Format: BinaryProtocol3 (P3)\n");
    printf("\n--- HEX DATA START ---\n");

    // 以16进制格式输出文件内容
    uint8_t buffer[16];
    size_t bytes_read;
    size_t offset = 0;

    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
        printf("%08X: ", (unsigned int)offset);

        // 输出16进制
        for (size_t i = 0; i < bytes_read; i++) {
            printf("%02X ", buffer[i]);
        }

        // 补齐空格
        for (size_t i = bytes_read; i < 16; i++) {
            printf("   ");
        }

        printf(" |");

        // 输出ASCII字符（如果可打印）
        for (size_t i = 0; i < bytes_read; i++) {
            char c = buffer[i];
            printf("%c", (c >= 32 && c <= 126) ? c : '.');
        }

        printf("|\n");
        offset += bytes_read;
    }

    fclose(file);

    printf("--- HEX DATA END ---\n");
    printf("\n💾 To save this file on PC:\n");
    printf("1. Copy all hex data above\n");
    printf("2. Use a hex editor to create %s\n", filename.c_str());
    printf("3. Save to: J:\\xiaozhi-esp32\\audio_files\\%s\n", filename.c_str());

    return 0;
}

int NetworkConsole::CmdTtsExportAll(int argc, char** argv) {
    printf("\n🚀 开始批量导出所有TTS音频文件...\n");
    printf("目标目录: J:\\xiaozhi-esp32\\audio_files\\\n\n");

    DIR* dir = opendir("/audio");
    if (!dir) {
        printf("❌ Cannot open /audio directory\n");
        return 1;
    }

    // 收集所有TTS文件
    std::vector<std::string> tts_files;
    struct dirent* entry;

    while ((entry = readdir(dir)) != nullptr) {
        if (strstr(entry->d_name, "tts_") && strstr(entry->d_name, ".p3")) {
            tts_files.push_back(entry->d_name);
        }
    }
    closedir(dir);

    if (tts_files.empty()) {
        printf("📭 没有找到TTS文件需要导出\n");
        return 0;
    }

    // 排序文件名
    std::sort(tts_files.begin(), tts_files.end());

    printf("📋 找到 %d 个TTS文件:\n", tts_files.size());
    for (size_t i = 0; i < tts_files.size(); i++) {
        printf("   %d. %s\n", (int)(i + 1), tts_files[i].c_str());
    }

    printf("\n=== BATCH_EXPORT_START ===\n");

    // 逐个导出文件
    for (const auto& filename : tts_files) {
        std::string full_path = "/audio/" + filename;

        // 检查文件
        struct stat file_stat;
        if (stat(full_path.c_str(), &file_stat) != 0) {
            printf("❌ File not found: %s\n", filename.c_str());
            continue;
        }

        // 打开文件
        FILE* file = fopen(full_path.c_str(), "rb");
        if (!file) {
            printf("❌ Cannot open file: %s\n", filename.c_str());
            continue;
        }

        printf("\n--- FILE_START: %s ---\n", filename.c_str());
        printf("SIZE: %ld\n", file_stat.st_size);
        printf("HEX_DATA:\n");

        // 输出文件的十六进制数据
        uint8_t buffer[32];  // 每行32字节
        size_t bytes_read;

        while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
            for (size_t i = 0; i < bytes_read; i++) {
                printf("%02X", buffer[i]);
            }
            printf("\n");
        }

        fclose(file);
        printf("--- FILE_END: %s ---\n", filename.c_str());
    }

    printf("=== BATCH_EXPORT_END ===\n");
    printf("\n🎉 批量导出完成! 共导出 %d 个文件\n", tts_files.size());
    printf("\n💡 使用说明:\n");
    printf("1. 复制上面的所有输出内容\n");
    printf("2. 运行电脑端的接收脚本: python receive_tts_export.py\n");
    printf("3. 或者手动解析十六进制数据创建文件\n");

    return 0;
}

// ========== 指令配置管理命令 ==========
// 暂时注释掉，等待完整实现后再启用
/*
static int CmdConfigStatus(int argc, char** argv) {
    printf("Command config system not implemented yet\n");
    return 0;
}
*/



void NetworkConsole::InitializeUartCommandManager() {
    if (uart_command_manager_) {
        ESP_LOGW(TAG, "UART command manager already initialized");
        return;
    }

    uart_command_manager_ = new communication::UartCommandManager();

    // 获取文本交互管理器
    auto& app = Application::GetInstance();
    auto text_manager = app.GetTextInteractionManager();

    if (!text_manager) {
        ESP_LOGE(TAG, "Text interaction manager not available for UART command manager");
        delete uart_command_manager_;
        uart_command_manager_ = nullptr;
        return;
    }

    // 初始化UART指令管理器（传递nullptr，因为TextCommands是静态类）
    esp_err_t err = uart_command_manager_->Initialize(nullptr, this);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize UART command manager: %s", esp_err_to_name(err));
        delete uart_command_manager_;
        uart_command_manager_ = nullptr;
        return;
    }

    // 设置响应回调函数，实际发送到UART2
    uart_command_manager_->SetResponseCallback([](const std::string& response) {
        ESP_LOGI(TAG, "📤 Sending UART response: %s", response.c_str());
        // 发送响应到UART2
        uart_write_bytes(UART_NUM_2, response.c_str(), response.length());
    });

    // 设置静态实例指针，供外部调用
    SetGlobalUartCommandManager(uart_command_manager_);

    ESP_LOGI(TAG, "UART command manager initialized successfully");
}

void NetworkConsole::ProcessUartData(const std::string& data) {
    if (!uart_command_manager_) {
        ESP_LOGW(TAG, "UART command manager not initialized");
        return;
    }

    ESP_LOGI(TAG, "🔗 PC client connected, processing UART data");
    uart_command_manager_->ProcessReceivedData(data);
}

// 静态全局UART命令管理器指针
static communication::UartCommandManager* g_uart_command_manager = nullptr;

void NetworkConsole::SetGlobalUartCommandManager(communication::UartCommandManager* manager) {
    g_uart_command_manager = manager;
}

communication::UartCommandManager* NetworkConsole::GetUartCommandManager() const {
    return uart_command_manager_.get();
}

// 全局函数，供汽车语音系统调用
extern "C" {
    void network_console_process_uart_data(const char* data, int len) {
        if (len <= 0 || !g_uart_command_manager) {
            ESP_LOGW("NetworkConsole", "⚠️ UART command manager not available, data length: %d", len);
            return;
        }

        ESP_LOGI("NetworkConsole", "🔗 Forwarding UART data to command manager: %d bytes", len);

        std::string data_str(data, len);
        g_uart_command_manager->ProcessReceivedData(data_str);
    }
}
