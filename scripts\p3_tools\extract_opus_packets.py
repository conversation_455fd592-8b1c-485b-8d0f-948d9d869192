#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取999.p3中的真实Opus数据包进行分析
"""

import struct
import os

def extract_and_analyze_opus_packets(p3_file):
    """
    提取并分析P3文件中的Opus数据包
    """
    print(f"分析文件: {p3_file}")
    print("=" * 60)
    
    packets = []
    
    with open(p3_file, 'rb') as f:
        packet_index = 0
        while True:
            # 读取4字节头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            # 读取Opus数据包
            opus_data = f.read(data_len)
            if len(opus_data) != data_len:
                break
            
            packets.append(opus_data)
            
            # 分析前几个数据包
            if packet_index < 5:
                print(f"\n数据包 {packet_index + 1}:")
                print(f"  大小: {len(opus_data)} 字节")
                print(f"  前16字节: {opus_data[:16].hex()}")
                
                # 分析Opus TOC字节
                if len(opus_data) > 0:
                    toc = opus_data[0]
                    config = (toc >> 3) & 0x1F
                    stereo = (toc >> 2) & 0x01
                    frame_count = toc & 0x03
                    
                    print(f"  TOC字节: 0x{toc:02X}")
                    print(f"  配置: {config}")
                    print(f"  立体声: {bool(stereo)}")
                    print(f"  帧数编码: {frame_count}")
            
            packet_index += 1
    
    print(f"\n总共提取了 {len(packets)} 个Opus数据包")
    
    # 保存第一个数据包用于分析
    if packets:
        with open("sample_opus_packet.bin", "wb") as f:
            f.write(packets[0])
        print(f"已保存第一个数据包到: sample_opus_packet.bin")
    
    return packets

def create_raw_opus_file(packets, output_file):
    """
    将Opus数据包重新组合成原始Opus流
    """
    with open(output_file, 'wb') as f:
        for packet in packets:
            f.write(packet)
    
    print(f"已创建原始Opus文件: {output_file}")

if __name__ == "__main__":
    # 分析999.p3
    packets = extract_and_analyze_opus_packets("999.p3")
    
    # 创建原始Opus文件用于测试
    if packets:
        create_raw_opus_file(packets, "999_extracted.opus")
