# 小智ESP32指令说明文档

## 📋 指令总览

小智ESP32设备支持多种类型的指令，通过串口控制台进行交互。所有指令都通过 `xiaozhi>` 提示符输入。

---

## 🌐 网络控制指令 (nethelp)

### 基础网络指令
| 指令 | 功能说明 | 用法示例 |
|------|----------|----------|
| `4g` | 切换到4G网络(ML307模块) | `xiaozhi> 4g` |
| `wifi` | 切换到WiFi网络 | `xiaozhi> wifi` |
| `status` | 显示当前网络状态 | `xiaozhi> status` |
| `nethelp` | 显示网络指令帮助 | `xiaozhi> nethelp` |
| `reboot` | 重启设备 | `xiaozhi> reboot` |

### 音频管理指令
| 指令 | 功能说明 | 用法示例 |
|------|----------|----------|
| `local` | 播放本地音频文件 | `xiaozhi> local` (列表)<br>`xiaozhi> local 001` (播放) |
| `audio_status` | 显示音频保存状态 | `xiaozhi> audio_status` |
| `audio_save` | 控制音频保存开关 | `xiaozhi> audio_save on`<br>`xiaozhi> audio_save off` |
| `tts_list` | 列出所有保存的TTS音频 | `xiaozhi> tts_list` |
| `tts_play` | 播放指定TTS音频文件 | `xiaozhi> tts_play 1` |
| `tts_clear` | 删除所有TTS音频文件 | `xiaozhi> tts_clear` |
| `tts_export` | 导出TTS音频文件信息 | `xiaozhi> tts_export` |

---

## 💬 文本交互指令 (texthelp)

### 基础交互指令
| 指令 | 功能说明 | 对应中文文本 | 用法示例 |
|------|----------|--------------|----------|
| `ask` | 发送任意文本给小智AI | 用户自定义 | `ask "今天天气怎么样"` |
| `say` | 简化版ask命令 | 用户自定义 | `say hello` |
| `hello` | 快速问候小智 | "你好小智" | `xiaozhi> hello` |
| `weather` | 查询天气信息 | "{城市}的天气怎么样" | `xiaozhi> weather`<br>`xiaozhi> weather 北京` |
| `time` | 询问当前时间 | "现在几点了" | `xiaozhi> time` |
| `joke` | 请小智讲笑话 | "给我讲个笑话" | `xiaozhi> joke` |
| `now` | 快速获取时间和天气 | 完整播报格式 | `xiaozhi> now` |

### 系统状态指令
| 指令 | 功能说明 | 用法示例 |
|------|----------|----------|
| `texthelp` | 显示文本交互帮助 | `xiaozhi> texthelp` |
| `textstatus` | 显示文本交互系统状态 | `xiaozhi> textstatus` |

### 特殊指令详解

#### `now` 指令
- **功能**: 播报完整的日期、时间、天气信息
- **对应文本**: "按照以下格式播报今日完整信息：今天是****年*月**日周*，农历*月初*。（ip地址天气：）阴天，28℃，西北风1级，空气质量优质。今天白天有中雨，记得带伞哦~"
- **特点**: 预设格式，包含日期、农历、天气、温度等完整信息

#### `ask` 指令
- **功能**: 最灵活的文本交互指令
- **支持**: 任意中文或英文文本
- **用法**: 
  - `ask "你好小智"` (推荐使用引号)
  - `ask hello` (单个英文词)
  - `ask weather` (预设命令)

---

## �️ 自定义指令配置

### cmdadd
- **功能**: 添加自定义指令
- **用法**: `cmdadd <指令名> <文本内容> [描述]`
- **示例**:
  - `cmdadd music "播放一首好听的音乐" "音乐播放指令"`
  - `cmdadd goodnight "晚安小智，明天见"`
- **说明**: 创建一个新的自定义指令，可以通过 `custom <指令名>` 来执行

### cmdlist
- **功能**: 列出所有自定义指令
- **用法**: `cmdlist`
- **说明**: 显示当前所有已配置的自定义指令及其内容

### cmdmodify
- **功能**: 修改自定义指令的文本内容
- **用法**: `cmdmodify <指令名> <新文本内容>`
- **示例**: `cmdmodify hello "你好小智，很高兴见到你"`
- **说明**: 修改已存在的自定义指令的文本内容

### cmdremove
- **功能**: 删除自定义指令
- **用法**: `cmdremove <指令名>`
- **示例**: `cmdremove music`
- **说明**: 删除指定的自定义指令

### cmdsave
- **功能**: 保存自定义指令到存储器
- **用法**: `cmdsave`
- **说明**: 将当前所有自定义指令保存到设备存储器，重启后自动加载

### cmdload
- **功能**: 从存储器加载自定义指令
- **用法**: `cmdload`
- **说明**: 从设备存储器加载之前保存的自定义指令

### custom
- **功能**: 执行自定义指令
- **用法**: `custom <指令名>`
- **示例**: `custom music`
- **说明**: 执行指定的自定义指令，发送对应的文本给小智

---

## �🔧 设备特定指令

### SenseCap Watcher设备专用
| 指令 | 功能说明 | 用法示例 |
|------|----------|----------|
| `battery` | 获取电池电量百分比 | `xiaozhi> battery` |
| `factory_reset` | 恢复出厂设置并重启 | `xiaozhi> factory_reset` |

---

## 📡 指令处理流程

### 文本交互指令流程
1. **输入指令** → 串口控制台接收
2. **指令解析** → 识别指令类型和参数
3. **文本转换** → 将指令转换为对应的中文文本
4. **发送请求** → 通过WebSocket发送给小智服务器
5. **接收响应** → 获取AI生成的音频回复
6. **音频播放** → 通过音频编解码器播放TTS音频

### 网络控制指令流程
1. **输入指令** → 串口控制台接收
2. **执行操作** → 直接执行网络切换/状态查询等操作
3. **反馈结果** → 在控制台显示操作结果

---

## 🎯 指令使用建议

### 最佳实践
1. **中文输入**: 使用ask指令时建议用引号包围中文文本
2. **网络切换**: 切换网络后设备会自动重启
3. **音频管理**: 定期清理TTS缓存文件释放存储空间
4. **状态查询**: 使用status和textstatus了解系统状态

### 常见用法
```bash
# 基础交互
xiaozhi> hello
xiaozhi> weather 上海
xiaozhi> time
xiaozhi> joke

# 自定义交互
xiaozhi> ask "帮我制定今天的学习计划"
xiaozhi> ask "推荐几首好听的歌曲"

# 系统管理
xiaozhi> status
xiaozhi> audio_status
xiaozhi> tts_list

# 网络管理
xiaozhi> wifi
xiaozhi> 4g
xiaozhi> reboot
```

---

## 📝 注意事项

1. **指令大小写**: 所有指令都是小写字母
2. **参数分隔**: 指令和参数之间用空格分隔
3. **中文编码**: 支持UTF-8编码的中文输入
4. **网络依赖**: 文本交互指令需要网络连接
5. **音频输出**: 确保音频输出设备正常工作
