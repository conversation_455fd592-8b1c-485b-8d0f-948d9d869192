#include "text_command_storage.h"
#include "esp_log.h"
#include <algorithm>
#include <cctype>
#include <cstring>

static const char* TAG = "TextCommandStorage";

// 静态常量定义
const char* TextCommandStorage::NVS_NAMESPACE = "uart_cmds";
const char* TextCommandStorage::KEY_PREFIX = "txt_";
const char* TextCommandStorage::COUNT_KEY = "count";
const size_t TextCommandStorage::MAX_COMMAND_NAME_LENGTH = 32;
const size_t TextCommandStorage::MAX_COMMAND_TEXT_LENGTH = 500;
const size_t TextCommandStorage::MAX_COMMAND_COUNT = 50;

TextCommandStorage::TextCommandStorage() 
    : nvs_handle_(0), initialized_(false) {
}

TextCommandStorage::~TextCommandStorage() {
    if (initialized_ && nvs_handle_ != 0) {
        nvs_close(nvs_handle_);
    }
}

esp_err_t TextCommandStorage::Initialize() {
    if (initialized_) {
        return ESP_OK;
    }

    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS namespace: %s", esp_err_to_name(err));
        return err;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "Text command storage initialized successfully");
    
    // 记录当前存储的指令数量
    size_t count = GetStoredCommandCount();
    ESP_LOGI(TAG, "Found %zu stored text commands", count);
    
    return ESP_OK;
}

esp_err_t TextCommandStorage::AddCommand(const std::string& name, const std::string& text) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Storage not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!IsValidCommandName(name)) {
        ESP_LOGE(TAG, "Invalid command name: %s", name.c_str());
        return ESP_ERR_INVALID_ARG;
    }

    if (!IsValidCommandText(text)) {
        ESP_LOGE(TAG, "Invalid command text (too long or empty)");
        return ESP_ERR_INVALID_ARG;
    }

    if (CommandExists(name)) {
        ESP_LOGE(TAG, "Command already exists: %s", name.c_str());
        return ESP_ERR_INVALID_ARG;
    }

    if (GetCommandCount() >= MAX_COMMAND_COUNT) {
        ESP_LOGE(TAG, "Maximum command count reached (%zu)", MAX_COMMAND_COUNT);
        return ESP_ERR_NO_MEM;
    }

    std::string key = GenerateKey(name);
    esp_err_t err = nvs_set_str(nvs_handle_, key.c_str(), text.c_str());
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save command %s: %s", name.c_str(), esp_err_to_name(err));
        return err;
    }

    err = nvs_commit(nvs_handle_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(err));
        return err;
    }

    UpdateCommandCount();
    ESP_LOGI(TAG, "Added command: %s -> %s", name.c_str(), text.c_str());
    return ESP_OK;
}

esp_err_t TextCommandStorage::ModifyCommand(const std::string& name, const std::string& new_text) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Storage not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!IsValidCommandName(name)) {
        ESP_LOGE(TAG, "Invalid command name: %s", name.c_str());
        return ESP_ERR_INVALID_ARG;
    }

    if (!IsValidCommandText(new_text)) {
        ESP_LOGE(TAG, "Invalid command text (too long or empty)");
        return ESP_ERR_INVALID_ARG;
    }

    if (!CommandExists(name)) {
        ESP_LOGE(TAG, "Command does not exist: %s", name.c_str());
        return ESP_ERR_NOT_FOUND;
    }

    std::string key = GenerateKey(name);
    esp_err_t err = nvs_set_str(nvs_handle_, key.c_str(), new_text.c_str());
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to modify command %s: %s", name.c_str(), esp_err_to_name(err));
        return err;
    }

    err = nvs_commit(nvs_handle_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "Modified command: %s -> %s", name.c_str(), new_text.c_str());
    return ESP_OK;
}

esp_err_t TextCommandStorage::DeleteCommand(const std::string& name) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Storage not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!IsValidCommandName(name)) {
        ESP_LOGE(TAG, "Invalid command name: %s", name.c_str());
        return ESP_ERR_INVALID_ARG;
    }

    if (!CommandExists(name)) {
        ESP_LOGE(TAG, "Command does not exist: %s", name.c_str());
        return ESP_ERR_NOT_FOUND;
    }

    std::string key = GenerateKey(name);
    esp_err_t err = nvs_erase_key(nvs_handle_, key.c_str());
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to delete command %s: %s", name.c_str(), esp_err_to_name(err));
        return err;
    }

    err = nvs_commit(nvs_handle_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(err));
        return err;
    }

    UpdateCommandCount();
    ESP_LOGI(TAG, "Deleted command: %s", name.c_str());
    return ESP_OK;
}

std::string TextCommandStorage::GetCommand(const std::string& name) {
    if (!initialized_ || !IsValidCommandName(name)) {
        return "";
    }

    std::string key = GenerateKey(name);
    size_t required_size = 0;
    
    // 首先获取所需的缓冲区大小
    esp_err_t err = nvs_get_str(nvs_handle_, key.c_str(), nullptr, &required_size);
    if (err != ESP_OK) {
        return "";
    }

    // 分配缓冲区并读取数据
    std::vector<char> buffer(required_size);
    err = nvs_get_str(nvs_handle_, key.c_str(), buffer.data(), &required_size);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get command %s: %s", name.c_str(), esp_err_to_name(err));
        return "";
    }

    return std::string(buffer.data());
}

std::map<std::string, std::string> TextCommandStorage::GetAllCommands() {
    std::map<std::string, std::string> commands;
    
    if (!initialized_) {
        return commands;
    }

    nvs_iterator_t it = nullptr;
    esp_err_t err = nvs_entry_find(NVS_DEFAULT_PART_NAME, NVS_NAMESPACE, NVS_TYPE_STR, &it);
    if (err != ESP_OK) {
        return commands;
    }

    while (it != nullptr) {
        nvs_entry_info_t info;
        nvs_entry_info(it, &info);

        std::string key = info.key;
        if (key.find(KEY_PREFIX) == 0 && key != COUNT_KEY) {
            // 提取指令名称（去掉前缀）
            std::string name = key.substr(strlen(KEY_PREFIX));
            std::string text = GetCommand(name);
            if (!text.empty()) {
                commands[name] = text;
            }
        }

        esp_err_t next_err = nvs_entry_next(&it);
        if (next_err != ESP_OK) {
            break;
        }
    }

    if (it != nullptr) {
        nvs_release_iterator(it);
    }
    return commands;
}

esp_err_t TextCommandStorage::ClearAllCommands() {
    if (!initialized_) {
        ESP_LOGE(TAG, "Storage not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 获取所有指令名称
    auto commands = GetAllCommands();
    
    // 逐个删除
    for (const auto& pair : commands) {
        std::string key = GenerateKey(pair.first);
        esp_err_t err = nvs_erase_key(nvs_handle_, key.c_str());
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to erase key %s: %s", key.c_str(), esp_err_to_name(err));
        }
    }

    esp_err_t err = nvs_commit(nvs_handle_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(err));
        return err;
    }

    UpdateCommandCount();
    ESP_LOGI(TAG, "Cleared all commands (%zu deleted)", commands.size());
    return ESP_OK;
}

esp_err_t TextCommandStorage::ImportCommands(const std::map<std::string, std::string>& commands) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Storage not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    size_t imported = 0;
    size_t failed = 0;

    for (const auto& pair : commands) {
        esp_err_t err = AddCommand(pair.first, pair.second);
        if (err == ESP_OK) {
            imported++;
        } else if (err == ESP_ERR_INVALID_ARG && CommandExists(pair.first)) {
            // 如果指令已存在，尝试修改
            err = ModifyCommand(pair.first, pair.second);
            if (err == ESP_OK) {
                imported++;
            } else {
                failed++;
            }
        } else {
            failed++;
        }
    }

    ESP_LOGI(TAG, "Import completed: %zu imported, %zu failed", imported, failed);
    return (failed == 0) ? ESP_OK : ESP_ERR_INVALID_ARG;
}

size_t TextCommandStorage::GetCommandCount() {
    if (!initialized_) {
        return 0;
    }
    
    return GetAllCommands().size();
}

bool TextCommandStorage::CommandExists(const std::string& name) {
    if (!initialized_ || !IsValidCommandName(name)) {
        return false;
    }

    std::string key = GenerateKey(name);
    size_t required_size = 0;
    esp_err_t err = nvs_get_str(nvs_handle_, key.c_str(), nullptr, &required_size);
    return (err == ESP_OK);
}

bool TextCommandStorage::IsValidCommandName(const std::string& name) {
    if (name.empty() || name.length() > MAX_COMMAND_NAME_LENGTH) {
        return false;
    }

    // 检查是否只包含字母、数字和下划线
    for (char c : name) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    // 不能以数字开头
    if (std::isdigit(name[0])) {
        return false;
    }

    return true;
}

bool TextCommandStorage::IsValidCommandText(const std::string& text) {
    return !text.empty() && text.length() <= MAX_COMMAND_TEXT_LENGTH;
}

std::string TextCommandStorage::GenerateKey(const std::string& name) {
    return std::string(KEY_PREFIX) + name;
}

esp_err_t TextCommandStorage::UpdateCommandCount() {
    if (!initialized_) {
        return ESP_ERR_INVALID_STATE;
    }

    size_t count = GetCommandCount();
    esp_err_t err = nvs_set_u32(nvs_handle_, COUNT_KEY, count);
    if (err == ESP_OK) {
        err = nvs_commit(nvs_handle_);
    }
    return err;
}

size_t TextCommandStorage::GetStoredCommandCount() {
    if (!initialized_) {
        return 0;
    }

    uint32_t count = 0;
    esp_err_t err = nvs_get_u32(nvs_handle_, COUNT_KEY, &count);
    if (err != ESP_OK) {
        return 0;
    }
    return count;
}
