#ifndef CAR_DATA_PARSER_H
#define CAR_DATA_PARSER_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "car_voice_trigger.h"

#ifdef __cplusplus
extern "C" {
#endif

// 协议类型定义
#define CAR_PROTOCOL_360            0x01        // 360协议
#define CAR_PROTOCOL_CUSTOM         0x02        // 自定义协议
#define CAR_PROTOCOL_AUTO_DETECT    0x00        // 自动检测

// 数据包头定义（根据文档修正）
#define CAR_PACKET_HEADER_360       0x2E        // 360协议包头（文档要求）
#define CAR_PACKET_HEADER_CUSTOM    0x55        // 自定义协议包头
#define PROTOCOL_HEAD_CODE          0x2E        // 协议头代码（文档标准）

// 数据类型定义 (根据360协议文档修正)
#define CAR_DATA_TYPE_KEY_INFO      0x02        // 按键信息
#define CAR_DATA_TYPE_VEHICLE_INFO  0x03        // 车身信息 (360协议标准)
#define CAR_DATA_TYPE_TIME_INFO     0x04        // 时间信息
#define CAR_DATA_TYPE_TOUCH_INFO    0x05        // 触摸信息
#define CAR_DATA_TYPE_VOICE_CTRL    0x09        // 语音控制

// 兼容性定义 (保持向后兼容)
#define CAR_DATA_TYPE_STATUS        CAR_DATA_TYPE_VEHICLE_INFO  // 车辆状态数据
#define CAR_DATA_TYPE_DOOR          0x10        // 自定义车门数据 (避免与360协议冲突)
#define CAR_DATA_TYPE_SPEED         0x11        // 自定义车速数据
#define CAR_DATA_TYPE_GEAR          0x12        // 自定义档位数据
#define CAR_DATA_TYPE_SIGNAL        0x13        // 自定义转向灯数据
#define CAR_DATA_TYPE_STEERING      0x14        // 自定义方向盘数据

// 解析器状态
typedef enum {
    PARSER_STATE_IDLE,                          // 空闲状态
    PARSER_STATE_HEADER,                        // 等待包头
    PARSER_STATE_TYPE,                          // 等待数据类型
    PARSER_STATE_LENGTH,                        // 等待长度
    PARSER_STATE_PAYLOAD,                       // 接收数据
    PARSER_STATE_CHECKSUM,                      // 等待校验和
    PARSER_STATE_COMPLETE                       // 解析完成
} parser_state_t;

// 解析器上下文
typedef struct {
    parser_state_t state;                       // 当前状态
    uint8_t protocol_type;                      // 协议类型
    car_data_packet_t current_packet;           // 当前数据包
    uint8_t payload_index;                      // 载荷索引
    uint8_t expected_checksum;                  // 期望校验和
    uint32_t packet_count;                      // 数据包计数
    uint32_t error_count;                       // 错误计数
    uint32_t last_valid_time;                   // 最后有效数据时间
} car_data_parser_t;

// 解析结果
typedef enum {
    PARSE_RESULT_OK,                            // 解析成功
    PARSE_RESULT_INCOMPLETE,                    // 数据不完整
    PARSE_RESULT_ERROR,                         // 解析错误
    PARSE_RESULT_CHECKSUM_ERROR,                // 校验和错误
    PARSE_RESULT_INVALID_HEADER,                // 无效包头
    PARSE_RESULT_INVALID_LENGTH,                // 无效长度
    PARSE_RESULT_BUFFER_OVERFLOW                // 缓冲区溢出
} parse_result_t;

// 初始化和清理
esp_err_t car_data_parser_init(car_data_parser_t* parser);
void car_data_parser_reset(car_data_parser_t* parser);
void car_data_parser_set_protocol(car_data_parser_t* parser, uint8_t protocol_type);

// 数据解析
parse_result_t car_data_parser_feed(car_data_parser_t* parser, uint8_t byte);
parse_result_t car_data_parser_feed_buffer(car_data_parser_t* parser, const uint8_t* data, size_t len);

// 数据包处理
bool car_data_parser_has_packet(const car_data_parser_t* parser);
car_data_packet_t car_data_parser_get_packet(car_data_parser_t* parser);

// 车辆状态更新
bool car_data_update_vehicle_state(const car_data_packet_t* packet, vehicle_state_t* state);

// 协议特定解析函数
bool car_data_parse_360_protocol(const car_data_packet_t* packet, vehicle_state_t* state);
bool car_data_parse_custom_protocol(const car_data_packet_t* packet, vehicle_state_t* state);

// 校验和计算
uint8_t car_data_calculate_checksum(const uint8_t* data, size_t len);
bool car_data_verify_checksum(const car_data_packet_t* packet);

// 协议检测
uint8_t car_data_detect_protocol(const uint8_t* data, size_t len);

// 调试和状态
void car_data_parser_print_stats(const car_data_parser_t* parser);
void car_data_packet_print(const car_data_packet_t* packet);

#ifdef __cplusplus
}
#endif

#endif // CAR_DATA_PARSER_H
