#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度对比两个P3文件的差异
"""

import struct
import os
import sys

def analyze_opus_packet_header(packet_data):
    """
    分析Opus数据包的头部信息
    """
    if len(packet_data) < 1:
        return "空数据包"
    
    # Opus数据包的第一个字节包含重要信息
    first_byte = packet_data[0]
    
    # 解析TOC (Table of Contents) 字节
    config = (first_byte >> 3) & 0x1F  # 配置号 (5位)
    stereo = (first_byte >> 2) & 0x01  # 立体声标志 (1位)
    frame_count_code = first_byte & 0x03  # 帧数编码 (2位)
    
    # 解析配置号对应的参数
    if config < 12:
        mode = "SILK"
        bandwidth = ["NB", "MB", "WB"][config // 4]
        frame_size = [10, 20, 40, 60][config % 4]
    elif config < 16:
        mode = "Hybrid"
        bandwidth = ["SWB", "FB"][(config - 12) // 2]
        frame_size = [10, 20][(config - 12) % 2]
    else:
        mode = "CELT"
        bandwidth = ["NB", "WB", "SWB", "FB"][(config - 16) // 4]
        frame_size = [2.5, 5, 10, 20][(config - 16) % 4]
    
    # 解析帧数
    if frame_count_code == 0:
        frame_count = 1
    elif frame_count_code == 1:
        frame_count = 2
    elif frame_count_code == 2:
        frame_count = 2  # VBR
    else:
        frame_count = "多帧"
    
    return {
        "config": config,
        "mode": mode,
        "bandwidth": bandwidth,
        "frame_size_ms": frame_size,
        "stereo": bool(stereo),
        "frame_count": frame_count,
        "first_bytes": packet_data[:8].hex() if len(packet_data) >= 8 else packet_data.hex()
    }

def deep_compare_p3_files(file1, file2):
    """
    深度对比两个P3文件
    """
    print(f"深度对比P3文件:")
    print(f"文件1: {file1}")
    print(f"文件2: {file2}")
    print("=" * 80)
    
    # 读取两个文件的数据包
    packets1 = read_p3_packets(file1)
    packets2 = read_p3_packets(file2)
    
    print(f"文件1数据包数量: {len(packets1)}")
    print(f"文件2数据包数量: {len(packets2)}")
    print()
    
    # 分析前几个数据包
    print("前5个数据包的详细分析:")
    print("-" * 80)
    
    for i in range(min(5, len(packets1), len(packets2))):
        print(f"\n数据包 {i+1}:")
        print(f"  文件1大小: {len(packets1[i])} 字节")
        print(f"  文件2大小: {len(packets2[i])} 字节")
        
        # 分析Opus头部
        analysis1 = analyze_opus_packet_header(packets1[i])
        analysis2 = analyze_opus_packet_header(packets2[i])
        
        print(f"  文件1 Opus分析: {analysis1}")
        print(f"  文件2 Opus分析: {analysis2}")
        
        # 比较前几个字节
        bytes1 = packets1[i][:16].hex() if len(packets1[i]) >= 16 else packets1[i].hex()
        bytes2 = packets2[i][:16].hex() if len(packets2[i]) >= 16 else packets2[i].hex()
        
        print(f"  文件1前16字节: {bytes1}")
        print(f"  文件2前16字节: {bytes2}")
        
        if bytes1 != bytes2:
            print(f"  ❌ 数据包内容不同!")
        else:
            print(f"  ✅ 数据包内容相同")
    
    # 统计分析
    print(f"\n统计分析:")
    print("-" * 80)
    
    if packets1:
        sizes1 = [len(p) for p in packets1]
        print(f"文件1数据包大小: 最小={min(sizes1)}, 最大={max(sizes1)}, 平均={sum(sizes1)/len(sizes1):.1f}")
    
    if packets2:
        sizes2 = [len(p) for p in packets2]
        print(f"文件2数据包大小: 最小={min(sizes2)}, 最大={max(sizes2)}, 平均={sum(sizes2)/len(sizes2):.1f}")

def read_p3_packets(file_path):
    """
    读取P3文件中的所有数据包
    """
    packets = []
    
    with open(file_path, 'rb') as f:
        while True:
            # 读取4字节头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            # 读取数据包
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
                
            packets.append(packet_data)
    
    return packets

def main():
    if len(sys.argv) != 3:
        print("使用方法: python compare_p3_files.py <文件1> <文件2>")
        sys.exit(1)
    
    file1 = sys.argv[1]
    file2 = sys.argv[2]
    
    if not os.path.exists(file1):
        print(f"文件不存在: {file1}")
        sys.exit(1)
    
    if not os.path.exists(file2):
        print(f"文件不存在: {file2}")
        sys.exit(1)
    
    deep_compare_p3_files(file1, file2)

if __name__ == "__main__":
    main()
