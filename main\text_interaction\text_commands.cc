#include "text_commands.h"
#include "text_interaction_manager.h"
#include "esp_console.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <cstdio>
#include <cstring>
#include <cJSON.h>
#include <vector>
#include <algorithm>
#include <cctype>



#ifdef CONFIG_ENABLE_CAR_VOICE_TRIGGER
#include "car_voice_trigger/car_voice_trigger.h"
#endif

static const char* TAG = "TextCommands";

namespace text_interaction {

// 静态成员变量定义
TextInteractionManager* TextCommands::manager_ = nullptr;
bool TextCommands::initialized_ = false;
std::map<std::string, CustomCommand> TextCommands::custom_commands_;
const char* TextCommands::CUSTOM_COMMANDS_NVS_KEY = "custom_cmds";

bool TextCommands::Initialize(TextInteractionManager* manager) {
    if (initialized_) {
        ESP_LOGW(TAG, "TextCommands already initialized");
        return true;
    }

    if (!manager) {
        ESP_LOGE(TAG, "TextInteractionManager is null");
        return false;
    }

    manager_ = manager;
    initialized_ = true;
    
    ESP_LOGI(TAG, "TextCommands initialized successfully");
    return true;
}

void TextCommands::RegisterCommands() {
    if (!initialized_ || !manager_) {
        ESP_LOGE(TAG, "TextCommands not properly initialized");
        return;
    }

    ESP_LOGI(TAG, "Registering text interaction commands...");

    // ask命令 - 发送任意文本
    const esp_console_cmd_t cmd_ask = {
        .command = "ask",
        .help = "Send text to Xiaozhi AI and get audio response",
        .hint = "[text_message]",
        .func = CmdAsk,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_ask));

    // say命令 - 简化版ask命令
    const esp_console_cmd_t cmd_say = {
        .command = "say",
        .help = "Simple text command (alternative to ask)",
        .hint = "[text_message]",
        .func = CmdSay,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_say));

    // now命令 - 快速获取时间和天气
    const esp_console_cmd_t cmd_now = {
        .command = "now",
        .help = "Get current time and weather quickly",
        .hint = nullptr,
        .func = CmdNow,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_now));

    // hello命令 - 快速问候
    const esp_console_cmd_t cmd_hello = {
        .command = "hello",
        .help = "Quick greeting to Xiaozhi",
        .hint = nullptr,
        .func = CmdHello,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_hello));

    // weather命令 - 天气查询
    const esp_console_cmd_t cmd_weather = {
        .command = "weather",
        .help = "Ask about weather",
        .hint = "[city]",
        .func = CmdWeather,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_weather));

    // time命令 - 时间查询
    const esp_console_cmd_t cmd_time = {
        .command = "time",
        .help = "Ask about current time",
        .hint = nullptr,
        .func = CmdTime,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_time));

    // joke命令 - 讲笑话
    const esp_console_cmd_t cmd_joke = {
        .command = "joke",
        .help = "Ask Xiaozhi to tell a joke",
        .hint = nullptr,
        .func = CmdJoke,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_joke));

    // texthelp命令 - 文本交互帮助
    const esp_console_cmd_t cmd_texthelp = {
        .command = "texthelp",
        .help = "Show text interaction commands help",
        .hint = nullptr,
        .func = CmdTextHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_texthelp));

    // textstatus命令 - 文本交互状态
    const esp_console_cmd_t cmd_textstatus = {
        .command = "textstatus",
        .help = "Show text interaction system status",
        .hint = nullptr,
        .func = CmdTextStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_textstatus));

    // 指令配置管理命令
    const esp_console_cmd_t cmd_cmdadd = {
        .command = "cmdadd",
        .help = "Add custom command: cmdadd <command> <text> [description]",
        .hint = "<command> <text> [description]",
        .func = CmdAdd,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdadd));

    const esp_console_cmd_t cmd_cmdlist = {
        .command = "cmdlist",
        .help = "List all custom commands",
        .hint = nullptr,
        .func = CmdList,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdlist));

    const esp_console_cmd_t cmd_cmdmodify = {
        .command = "cmdmodify",
        .help = "Modify custom command: cmdmodify <command> <new_text>",
        .hint = "<command> <new_text>",
        .func = CmdModify,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdmodify));

    const esp_console_cmd_t cmd_cmdremove = {
        .command = "cmdremove",
        .help = "Remove custom command: cmdremove <command>",
        .hint = "<command>",
        .func = CmdRemove,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdremove));

    const esp_console_cmd_t cmd_cmdsave = {
        .command = "cmdsave",
        .help = "Save custom commands to storage",
        .hint = nullptr,
        .func = CmdSave,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdsave));

    const esp_console_cmd_t cmd_cmdload = {
        .command = "cmdload",
        .help = "Load custom commands from storage",
        .hint = nullptr,
        .func = CmdLoad,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_cmdload));

    // 注册通用自定义指令执行器
    const esp_console_cmd_t cmd_custom = {
        .command = "custom",
        .help = "Execute custom command: custom <command_name>",
        .hint = "<command_name>",
        .func = CmdCustom,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_custom));

    // 加载已保存的自定义指令
    LoadCustomCommands();

    // 注册已加载的自定义指令到控制台
    RegisterCustomCommands();

#ifdef CONFIG_ENABLE_CAR_VOICE_TRIGGER
    // 汽车语音触发功能调试命令
    const esp_console_cmd_t cmd_car_status = {
        .command = "carstatus",
        .help = "Show car voice trigger system status",
        .hint = nullptr,
        .func = CmdCarStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_car_status));

    const esp_console_cmd_t cmd_car_play = {
        .command = "carplay",
        .help = "Manually play car voice: carplay <voice_id>",
        .hint = "<voice_id>",
        .func = CmdCarPlay,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_car_play));

    const esp_console_cmd_t cmd_car_inject = {
        .command = "carinject",
        .help = "Inject test car data: carinject <hex_data>",
        .hint = "<hex_data>",
        .func = CmdCarInject,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_car_inject));

    const esp_console_cmd_t cmd_car_enable = {
        .command = "carenable",
        .help = "Enable/disable car voice trigger: carenable <0|1>",
        .hint = "<0|1>",
        .func = CmdCarEnable,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_car_enable));

    ESP_LOGI(TAG, "Car voice trigger commands registered successfully");
#endif



    ESP_LOGI(TAG, "All text interaction commands registered successfully");
}

TextInteractionManager* TextCommands::GetManager() {
    return manager_;
}

bool TextCommands::ExecuteTextCommand(const std::string& text) {
    if (!initialized_ || !manager_) {
        ESP_LOGE(TAG, "TextCommands not properly initialized");
        return false;
    }

    if (text.empty()) {
        ESP_LOGE(TAG, "Text command is empty");
        return false;
    }

    ESP_LOGI(TAG, "Executing text command: %s", text.c_str());

    // 使用文本交互管理器发送消息
    bool success = manager_->SendTextToXiaozhi(text);
    if (success) {
        ESP_LOGI(TAG, "Text command executed successfully");
    } else {
        ESP_LOGE(TAG, "Failed to execute text command");
    }

    return success;
}

// ========== 命令处理函数实现 ==========

int TextCommands::CmdAsk(int argc, char** argv) {
    // 调试信息
    printf("🔍 Debug: argc=%d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("🔍 Debug: argv[%d]='%s'\n", i, argv[i]);
    }

    if (argc < 2) {
        printf("💡 提示: 请使用以下方式之一:\n");
        printf("   1. ask \"今天天气怎么样\"  (推荐，使用引号)\n");
        printf("   2. ask hello             (单个英文词)\n");
        printf("   3. ask weather           (预设命令)\n");
        printf("   4. ask tianqi            (天气查询)\n");
        printf("   5. ask shijian           (时间查询)\n");
        return 1;
    }

    std::string message;
    if (!BuildMessageFromArgs(argc, argv, 1, message)) {
        printf("❌ 错误: 无法构建消息或中文编码问题\n");
        printf("💡 建议: 尝试使用英文关键词如 weather, time, hello\n");
        return 1;
    }

    // 检查是否是预设的中文关键词映射
    if (message == "tianqi") {
        message = "今天天气怎么样";
        printf("🔄 转换为中文: %s\n", message.c_str());
    } else if (message == "shijian") {
        message = "现在几点了";
        printf("🔄 转换为中文: %s\n", message.c_str());
    } else if (message == "weather") {
        message = "今天天气怎么样";
        printf("🔄 转换为中文: %s\n", message.c_str());
    } else if (message == "time") {
        message = "现在几点了";
        printf("🔄 转换为中文: %s\n", message.c_str());
    } else if (message == "hello") {
        message = "你好";
        printf("🔄 转换为中文: %s\n", message.c_str());
    }

    printf("🔍 Debug: final message='%s'\n", message.c_str());
    return SendMessageWithStatus(message, "发送消息") ? 0 : 1;
}

int TextCommands::CmdRaw(int argc, char** argv) {
    // 原始命令，直接发送所有参数，不进行编码转换
    printf("🔍 Raw Debug: argc=%d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("🔍 Raw Debug: argv[%d]='%s' (len=%d)\n", i, argv[i], strlen(argv[i]));
        // 打印十六进制字节
        printf("🔍 Raw Hex: ");
        for (int j = 0; j < strlen(argv[i]); j++) {
            printf("%02X ", (unsigned char)argv[i][j]);
        }
        printf("\n");
    }

    if (argc < 2) {
        printf("💡 raw命令用法:\n");
        printf("   raw <message>  - 直接发送原始消息\n");
        return 1;
    }

    std::string message;
    if (!BuildMessageFromArgs(argc, argv, 1, message)) {
        printf("❌ 错误: 无法构建消息\n");
        return 1;
    }

    printf("📤 原始发送: %s (长度: %d)\n", message.c_str(), message.length());
    return SendMessageWithStatus(message, "原始发送") ? 0 : 1;
}

int TextCommands::CmdSay(int argc, char** argv) {
    // 简化版本的ask命令，专门处理中文输入
    printf("🔍 Say Debug: argc=%d\n", argc);
    for (int i = 0; i < argc; i++) {
        printf("🔍 Say Debug: argv[%d]='%s'\n", i, argv[i]);
    }

    if (argc < 2) {
        printf("💡 say命令用法:\n");
        printf("   say hello\n");
        printf("   say \"你好小智\"\n");
        printf("   say weather\n");
        return 1;
    }

    std::string message;
    if (!BuildMessageFromArgs(argc, argv, 1, message)) {
        printf("❌ 错误: 无法构建消息\n");
        return 1;
    }

    printf("📤 发送消息: %s\n", message.c_str());
    return SendMessageWithStatus(message, "发送消息") ? 0 : 1;
}

int TextCommands::CmdNow(int argc, char** argv) {
    // now命令 - 直接发送预设的时间天气查询指令
    std::string message = "按照以下格式播报今日完整信息：今天是****年*月**日周*，农历*月初*。（ip地址天气：）阴天，28℃，西北风1级，空气质量优质。今天白天有中雨，记得带伞哦~";

    printf("🚀 快速查询时间和天气...\n");
    printf("📤 发送指令: %s\n", message.c_str());

    return SendMessageWithStatus(message, "快速查询") ? 0 : 1;
}

int TextCommands::CmdHello(int argc, char** argv) {
    return SendMessageWithStatus("你好小智", "向小智问好") ? 0 : 1;
}

int TextCommands::CmdWeather(int argc, char** argv) {
    std::string city = (argc > 1) ? argv[1] : "当前位置";
    std::string message = city + "的天气怎么样";
    
    char action[64];
    snprintf(action, sizeof(action), "查询%s天气", city.c_str());
    
    return SendMessageWithStatus(message, action) ? 0 : 1;
}

int TextCommands::CmdTime(int argc, char** argv) {
    return SendMessageWithStatus("现在几点了", "询问当前时间") ? 0 : 1;
}

int TextCommands::CmdJoke(int argc, char** argv) {
    return SendMessageWithStatus("给我讲个笑话", "请小智讲笑话") ? 0 : 1;
}

int TextCommands::CmdTextHelp(int argc, char** argv) {
    printf("\n=== 小智文本交互命令 ===\n");
    printf("ask <message>  - 发送任意文本给小智\n");
    printf("hello          - 向小智问好\n");
    printf("weather [city] - 查询天气 (默认当前位置)\n");
    printf("time           - 询问当前时间\n");
    printf("joke           - 请小智讲个笑话\n");
    printf("texthelp       - 显示此帮助信息\n");
    printf("textstatus     - 显示文本交互系统状态\n");
    printf("\n使用示例:\n");
    printf("  ask 今天天气怎么样\n");
    printf("  weather 北京\n");
    printf("  hello\n");
    printf("========================\n\n");
    return 0;
}

int TextCommands::CmdTextStatus(int argc, char** argv) {
    if (!manager_) {
        printf("❌ 错误: 文本交互管理器未初始化\n");
        return 1;
    }

    printf("\n=== 文本交互系统状态 ===\n");
    printf("%s\n", manager_->GetStatusDescription().c_str());
    printf("========================\n\n");
    return 0;
}

// ========== 辅助函数实现 ==========

bool TextCommands::BuildMessageFromArgs(int argc, char** argv, int start_index, std::string& message) {
    if (argc <= start_index) {
        return false;
    }

    message.clear();
    for (int i = start_index; i < argc; i++) {
        if (i > start_index) {
            message += " ";
        }

        // 检查参数是否为空（中文编码问题导致）
        if (argv[i] && strlen(argv[i]) > 0) {
            message += argv[i];
        }
    }

    // 如果消息为空或只包含空格，返回false
    std::string trimmed = message;
    trimmed.erase(0, trimmed.find_first_not_of(" \t\n\r"));
    trimmed.erase(trimmed.find_last_not_of(" \t\n\r") + 1);

    return !trimmed.empty();
}

void TextCommands::PrintUsage(const char* command, const char* usage, const char* example) {
    printf("用法: %s\n", usage);
    if (example) {
        printf("示例: %s\n", example);
    }
}

bool TextCommands::SendMessageWithStatus(const std::string& message, const char* action_description) {
    if (!manager_) {
        printf("❌ 错误: 文本交互管理器未初始化\n");
        return false;
    }

    printf("🚀 %s...\n", action_description);
    return manager_->SendTextToXiaozhi(message);
}

// ========== 指令配置管理功能实现 ==========

bool TextCommands::AddCustomCommand(const std::string& command, const std::string& text, const std::string& description) {
    if (!initialized_) {
        ESP_LOGE(TAG, "TextCommands not initialized");
        return false;
    }

    if (command.empty() || text.empty()) {
        ESP_LOGE(TAG, "Command name and text cannot be empty");
        return false;
    }

    // 检查是否与内置命令冲突
    const char* builtin_commands[] = {
        "ask", "say", "hello", "weather", "time", "joke", "now",
        "texthelp", "textstatus", "raw",
        "cmdadd", "cmdlist", "cmdmodify", "cmdremove", "cmdsave", "cmdload"
    };

    for (const char* builtin : builtin_commands) {
        if (command == builtin) {
            ESP_LOGE(TAG, "Cannot override builtin command: %s", command.c_str());
            return false;
        }
    }

    // 添加到映射表
    custom_commands_[command] = CustomCommand(text, description);

    ESP_LOGI(TAG, "Added custom command: %s -> %s", command.c_str(), text.c_str());
    return true;
}

bool TextCommands::RemoveCustomCommand(const std::string& command) {
    if (!initialized_) {
        ESP_LOGE(TAG, "TextCommands not initialized");
        return false;
    }

    auto it = custom_commands_.find(command);
    if (it == custom_commands_.end()) {
        ESP_LOGE(TAG, "Custom command not found: %s", command.c_str());
        return false;
    }

    // 从映射表删除
    custom_commands_.erase(it);

    ESP_LOGI(TAG, "Removed custom command: %s", command.c_str());
    return true;
}

bool TextCommands::ModifyCustomCommand(const std::string& command, const std::string& new_text) {
    if (!initialized_) {
        ESP_LOGE(TAG, "TextCommands not initialized");
        return false;
    }

    auto it = custom_commands_.find(command);
    if (it == custom_commands_.end()) {
        ESP_LOGE(TAG, "Custom command not found: %s", command.c_str());
        return false;
    }

    if (new_text.empty()) {
        ESP_LOGE(TAG, "New text cannot be empty");
        return false;
    }

    // 更新文本内容
    it->second.text = new_text;

    ESP_LOGI(TAG, "Modified custom command: %s -> %s", command.c_str(), new_text.c_str());
    return true;
}

void TextCommands::ListCustomCommands() {
    printf("\n=== 自定义指令列表 ===\n");

    if (custom_commands_.empty()) {
        printf("暂无自定义指令\n");
    } else {
        printf("共有 %d 个自定义指令:\n\n", custom_commands_.size());

        for (const auto& pair : custom_commands_) {
            printf("指令: %s\n", pair.first.c_str());
            printf("文本: %s\n", pair.second.text.c_str());
            if (!pair.second.description.empty()) {
                printf("描述: %s\n", pair.second.description.c_str());
            }
            printf("---\n");
        }
    }

    printf("使用方法:\n");
    printf("  cmdadd <指令名> <文本内容> [描述]  - 添加指令\n");
    printf("  cmdmodify <指令名> <新文本>        - 修改指令\n");
    printf("  cmdremove <指令名>               - 删除指令\n");
    printf("  cmdsave                         - 保存到存储器\n");
    printf("  cmdload                         - 从存储器加载\n");
    printf("========================\n\n");
}

bool TextCommands::SaveCustomCommands() {
    if (!initialized_) {
        ESP_LOGE(TAG, "TextCommands not initialized");
        return false;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("text_cmds", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS handle: %s", esp_err_to_name(err));
        return false;
    }

    // 创建JSON对象
    cJSON* json = cJSON_CreateObject();
    if (!json) {
        ESP_LOGE(TAG, "Failed to create JSON object");
        nvs_close(nvs_handle);
        return false;
    }

    // 添加所有自定义指令到JSON
    for (const auto& pair : custom_commands_) {
        cJSON* cmd_obj = cJSON_CreateObject();
        cJSON_AddStringToObject(cmd_obj, "text", pair.second.text.c_str());
        cJSON_AddStringToObject(cmd_obj, "description", pair.second.description.c_str());
        cJSON_AddItemToObject(json, pair.first.c_str(), cmd_obj);
    }

    // 转换为字符串
    char* json_string = cJSON_Print(json);
    if (!json_string) {
        ESP_LOGE(TAG, "Failed to convert JSON to string");
        cJSON_Delete(json);
        nvs_close(nvs_handle);
        return false;
    }

    // 保存到NVS
    err = nvs_set_str(nvs_handle, CUSTOM_COMMANDS_NVS_KEY, json_string);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save custom commands: %s", esp_err_to_name(err));
        free(json_string);
        cJSON_Delete(json);
        nvs_close(nvs_handle);
        return false;
    }

    err = nvs_commit(nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(err));
    }

    free(json_string);
    cJSON_Delete(json);
    nvs_close(nvs_handle);

    ESP_LOGI(TAG, "Saved %d custom commands to storage", custom_commands_.size());
    return true;
}

bool TextCommands::LoadCustomCommands() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("text_cmds", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGI(TAG, "No custom commands storage found, starting fresh");
        return true; // 不是错误，只是没有保存的数据
    }

    // 获取数据大小
    size_t required_size = 0;
    err = nvs_get_str(nvs_handle, CUSTOM_COMMANDS_NVS_KEY, nullptr, &required_size);
    if (err != ESP_OK) {
        ESP_LOGI(TAG, "No custom commands found in storage");
        nvs_close(nvs_handle);
        return true;
    }

    // 分配内存并读取数据
    char* json_string = (char*)malloc(required_size);
    if (!json_string) {
        ESP_LOGE(TAG, "Failed to allocate memory for JSON string");
        nvs_close(nvs_handle);
        return false;
    }

    err = nvs_get_str(nvs_handle, CUSTOM_COMMANDS_NVS_KEY, json_string, &required_size);
    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read custom commands: %s", esp_err_to_name(err));
        free(json_string);
        return false;
    }

    // 解析JSON
    cJSON* json = cJSON_Parse(json_string);
    free(json_string);

    if (!json) {
        ESP_LOGE(TAG, "Failed to parse JSON");
        return false;
    }

    // 清空现有的自定义指令
    custom_commands_.clear();

    // 加载所有指令
    cJSON* cmd_item = nullptr;
    cJSON_ArrayForEach(cmd_item, json) {
        const char* cmd_name = cmd_item->string;
        if (!cmd_name) continue;

        cJSON* text_item = cJSON_GetObjectItem(cmd_item, "text");
        cJSON* desc_item = cJSON_GetObjectItem(cmd_item, "description");

        if (!text_item || !cJSON_IsString(text_item)) continue;

        std::string text = text_item->valuestring;
        std::string description = (desc_item && cJSON_IsString(desc_item)) ? desc_item->valuestring : "";

        // 添加到映射表
        custom_commands_[cmd_name] = CustomCommand(text, description);
    }

    cJSON_Delete(json);
    ESP_LOGI(TAG, "Loaded %d custom commands from storage", custom_commands_.size());
    return true;
}

// 动态注册和注销功能（简化实现）
bool TextCommands::RegisterCustomCommand(const std::string& command, const CustomCommand& custom_cmd) {
    // 由于ESP-IDF的esp_console不支持动态注册/注销命令
    // 我们使用统一的 'custom <command>' 方式来执行自定义指令
    ESP_LOGI(TAG, "Registered custom command: %s", command.c_str());
    return true;
}

bool TextCommands::UnregisterCustomCommand(const std::string& command) {
    // ESP-IDF的esp_console不支持动态注销，这里只是记录
    ESP_LOGI(TAG, "Unregistered custom command: %s", command.c_str());
    return true;
}

void TextCommands::RegisterCustomCommands() {
    // 简化实现：由于ESP-IDF控制台系统的限制，我们不能动态注册命令
    // 自定义指令将通过 'custom <command>' 的方式执行
    // 这里只是记录已加载的自定义指令
    ESP_LOGI(TAG, "Custom commands loaded: %d", custom_commands_.size());
    for (const auto& pair : custom_commands_) {
        ESP_LOGI(TAG, "  - %s: %s", pair.first.c_str(), pair.second.text.c_str());
    }
}

// ========== 配置管理命令处理函数 ==========

int TextCommands::CmdAdd(int argc, char** argv) {
    if (argc < 3) {
        printf("用法: cmdadd <指令名> <文本内容> [描述]\n");
        printf("示例: cmdadd music 播放音乐 音乐指令\n");
        printf("注意: 参数中不要使用引号，用空格分隔\n");
        return 1;
    }

    std::string command = argv[1];
    std::string text = "";
    std::string description = "";

    // 将所有参数从第2个开始连接成文本内容
    // 如果有3个或更多参数，最后一个作为描述
    if (argc == 3) {
        // 只有指令名和文本内容
        text = argv[2];
    } else if (argc == 4) {
        // 指令名、文本内容、描述
        text = argv[2];
        description = argv[3];
    } else {
        // 多个参数，将中间的连接为文本，最后一个作为描述
        for (int i = 2; i < argc - 1; i++) {
            if (i > 2) text += " ";
            text += argv[i];
        }
        description = argv[argc - 1];
    }

    if (AddCustomCommand(command, text, description)) {
        printf("✅ 成功添加自定义指令: %s\n", command.c_str());
        printf("📝 文本内容: %s\n", text.c_str());
        if (!description.empty()) {
            printf("📋 描述: %s\n", description.c_str());
        }
        printf("💡 提示: 使用 'cmdsave' 保存到存储器\n");
        return 0;
    } else {
        printf("❌ 添加自定义指令失败\n");
        return 1;
    }
}

int TextCommands::CmdList(int argc, char** argv) {
    ListCustomCommands();
    return 0;
}

int TextCommands::CmdModify(int argc, char** argv) {
    if (argc < 3) {
        printf("用法: cmdmodify <指令名> <新文本内容>\n");
        printf("示例: cmdmodify hello \"你好小智，很高兴见到你\"\n");
        return 1;
    }

    std::string command = argv[1];
    std::string new_text = argv[2];

    if (ModifyCustomCommand(command, new_text)) {
        printf("✅ 成功修改自定义指令: %s\n", command.c_str());
        printf("📝 新文本内容: %s\n", new_text.c_str());
        printf("💡 提示: 使用 'cmdsave' 保存到存储器\n");
        return 0;
    } else {
        printf("❌ 修改自定义指令失败\n");
        return 1;
    }
}

int TextCommands::CmdRemove(int argc, char** argv) {
    if (argc < 2) {
        printf("用法: cmdremove <指令名>\n");
        printf("示例: cmdremove music\n");
        return 1;
    }

    std::string command = argv[1];

    if (RemoveCustomCommand(command)) {
        printf("✅ 成功删除自定义指令: %s\n", command.c_str());
        printf("💡 提示: 使用 'cmdsave' 保存到存储器\n");
        return 0;
    } else {
        printf("❌ 删除自定义指令失败\n");
        return 1;
    }
}

int TextCommands::CmdSave(int argc, char** argv) {
    if (SaveCustomCommands()) {
        printf("✅ 成功保存 %d 个自定义指令到存储器\n", custom_commands_.size());
        return 0;
    } else {
        printf("❌ 保存自定义指令失败\n");
        return 1;
    }
}

int TextCommands::CmdLoad(int argc, char** argv) {
    if (LoadCustomCommands()) {
        printf("✅ 成功从存储器加载 %d 个自定义指令\n", custom_commands_.size());
        return 0;
    } else {
        printf("❌ 加载自定义指令失败\n");
        return 1;
    }
}

int TextCommands::CmdCustom(int argc, char** argv) {
    if (argc < 2) {
        printf("用法: custom <指令名>\n");
        printf("示例: custom music\n");
        printf("使用 'cmdlist' 查看所有可用的自定义指令\n");
        return 1;
    }

    std::string command = argv[1];
    auto it = custom_commands_.find(command);
    if (it == custom_commands_.end()) {
        printf("❌ 未找到自定义指令: %s\n", command.c_str());
        printf("使用 'cmdlist' 查看所有可用的自定义指令\n");
        return 1;
    }

    // 发送自定义指令对应的文本
    if (!manager_) {
        printf("❌ 文本交互管理器未初始化\n");
        return 1;
    }

    printf("🚀 执行自定义指令: %s\n", command.c_str());
    printf("📤 发送文本: %s\n", it->second.text.c_str());

    bool success = manager_->SendTextToXiaozhi(it->second.text);
    if (success) {
        printf("✅ 指令执行成功\n");
        return 0;
    } else {
        printf("❌ 指令执行失败\n");
        return 1;
    }
}

#ifdef CONFIG_ENABLE_CAR_VOICE_TRIGGER
// ========== 汽车语音触发调试命令实现 ==========

int TextCommands::CmdCarStatus(int argc, char** argv) {
    printf("\n=== 汽车语音触发系统状态 ===\n");

    car_voice_trigger_status_t status;
    esp_err_t ret = car_voice_trigger_get_status(&status);

    if (ret == ESP_OK) {
        printf("🚗 系统状态: %s\n", status.enabled ? "启用" : "禁用");
        printf("🔊 语音播放: %s\n", status.voice_enabled ? "启用" : "禁用");
        printf("📊 接收数据包: %lu\n", status.packets_received);
        printf("🎵 播放语音: %lu\n", status.voice_played_count);
        printf("❌ 解析错误: %lu\n", status.parse_errors);
        printf("⏰ 最后更新: %lu ms\n", status.last_update_time);
        printf("🔗 协议类型: %d\n", status.protocol_type);

        if (status.packets_received > 0) {
            float error_rate = (float)status.parse_errors / status.packets_received * 100.0f;
            printf("📈 错误率: %.2f%%\n", error_rate);
        }

        printf("🚪 车门状态: 驾驶员=%d, 乘客=%d, 后左=%d, 后右=%d\n",
               status.vehicle_state.doors.driver_door,
               status.vehicle_state.doors.passenger_door,
               status.vehicle_state.doors.rear_left_door,
               status.vehicle_state.doors.rear_right_door);
        printf("🔄 转向灯: 左=%d, 右=%d\n",
               status.vehicle_state.turn_signals.left_signal,
               status.vehicle_state.turn_signals.right_signal);
        printf("⚡ ACC状态: %d\n", status.vehicle_state.acc_status);
        printf("🚗 档位: %d\n", status.vehicle_state.gear_position);
        printf("🏃 车速: %d km/h\n", status.vehicle_state.vehicle_speed);

        printf("========================\n\n");
        return 0;
    } else {
        printf("❌ 获取汽车语音触发状态失败: %s\n", esp_err_to_name(ret));
        return 1;
    }
}

int TextCommands::CmdCarPlay(int argc, char** argv) {
    if (argc < 2) {
        printf("💡 用法: carplay <voice_id>\n");
        printf("示例: carplay 2  (播放002.p3语音文件)\n");
        printf("可用语音ID:\n");
        printf("  1 - 欢迎语音\n");
        printf("  2 - 车门开启提醒\n");
        printf("  3 - 车门关闭提醒\n");
        printf("  4 - 左转向灯提醒\n");
        printf("  5 - 右转向灯提醒\n");
        printf("  6 - 倒车提醒\n");
        printf("  7 - 超速提醒\n");
        return 1;
    }

    int voice_id = atoi(argv[1]);
    if (voice_id < 1 || voice_id > 999) {
        printf("❌ 语音ID必须在1-999范围内\n");
        return 1;
    }

    printf("🎵 手动播放汽车语音: %d\n", voice_id);
    esp_err_t ret = car_voice_trigger_play_voice(voice_id, CAR_VOICE_PRIORITY_HIGH);

    if (ret == ESP_OK) {
        printf("✅ 语音播放请求已发送\n");
        return 0;
    } else {
        printf("❌ 语音播放失败: %s\n", esp_err_to_name(ret));
        return 1;
    }
}

int TextCommands::CmdCarInject(int argc, char** argv) {
    if (argc < 2) {
        printf("💡 用法: carinject <hex_data>\n");
        printf("示例: carinject AA010800010203040506\n");
        printf("数据格式: [包头][类型][长度][数据][校验和]\n");
        printf("测试数据包:\n");
        printf("  AA010800010203040506 - 360协议状态数据\n");
        printf("  55010800010203040506 - 自定义协议状态数据\n");
        return 1;
    }

    std::string hex_str = argv[1];

    // 移除空格和非十六进制字符
    hex_str.erase(std::remove_if(hex_str.begin(), hex_str.end(),
                                [](char c) { return !std::isxdigit(c); }),
                  hex_str.end());

    if (hex_str.length() % 2 != 0) {
        printf("❌ 十六进制数据长度必须是偶数\n");
        return 1;
    }

    if (hex_str.length() > 512) {  // 限制最大256字节
        printf("❌ 数据包太大，最大256字节\n");
        return 1;
    }

    // 转换十六进制字符串为字节数组
    std::vector<uint8_t> data;
    for (size_t i = 0; i < hex_str.length(); i += 2) {
        std::string byte_str = hex_str.substr(i, 2);
        uint8_t byte_val = (uint8_t)strtol(byte_str.c_str(), nullptr, 16);
        data.push_back(byte_val);
    }

    printf("🧪 注入测试数据: %s (%d字节)\n", hex_str.c_str(), data.size());

    esp_err_t ret = car_voice_trigger_inject_data(data.data(), data.size());

    if (ret == ESP_OK) {
        printf("✅ 测试数据注入成功\n");
        return 0;
    } else {
        printf("❌ 测试数据注入失败: %s\n", esp_err_to_name(ret));
        return 1;
    }
}

int TextCommands::CmdCarEnable(int argc, char** argv) {
    if (argc < 2) {
        printf("💡 用法: carenable <0|1>\n");
        printf("  0 - 禁用汽车语音触发\n");
        printf("  1 - 启用汽车语音触发\n");
        return 1;
    }

    int enable = atoi(argv[1]);
    if (enable != 0 && enable != 1) {
        printf("❌ 参数必须是0或1\n");
        return 1;
    }

    esp_err_t ret;
    if (enable) {
        printf("🚗 启用汽车语音触发功能...\n");
        ret = car_voice_trigger_enable(true);
    } else {
        printf("🚗 禁用汽车语音触发功能...\n");
        ret = car_voice_trigger_enable(false);
    }

    if (ret == ESP_OK) {
        printf("✅ 汽车语音触发功能已%s\n", enable ? "启用" : "禁用");
        return 0;
    } else {
        printf("❌ 操作失败: %s\n", esp_err_to_name(ret));
        return 1;
    }
}

#endif // CONFIG_ENABLE_CAR_VOICE_TRIGGER



} // namespace text_interaction
