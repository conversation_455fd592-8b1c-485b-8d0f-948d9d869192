#ifndef CAR_VOICE_TRIGGER_H
#define CAR_VOICE_TRIGGER_H

#include <stdint.h>
#include <stdbool.h>
#include <esp_err.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <driver/uart.h>

#ifdef __cplusplus
extern "C" {
#endif

// 配置参数
#define CAR_UART_PORT           UART_NUM_2      // 使用UART2
#define CAR_UART_TX_PIN         8               // 使用空闲GPIO8 (避免与I2S和UART命令管理器冲突)
#define CAR_UART_RX_PIN         9               // 使用空闲GPIO9 (避免与I2S和UART命令管理器冲突)
#define CAR_UART_BAUD_RATE      19200           // 汽车通信波特率
#define CAR_UART_BUF_SIZE       1024            // 接收缓冲区
#define CAR_DATA_QUEUE_SIZE     10              // 数据队列大小

// 语音文件配置
#define VOICE_FILE_PATH_PREFIX  "/audio/"       // 音频文件路径前缀
#define MAX_VOICE_ID            17              // 最大语音ID
#define MIN_VOICE_ID            1               // 最小语音ID

// 触发条件定义
#define TRIGGER_ACC_ON              0x01        // ACC开启
#define TRIGGER_R_GEAR              0x02        // R档
#define TRIGGER_D_GEAR              0x03        // D档
#define TRIGGER_DRIVER_DOOR         0x04        // 主驾车门
#define TRIGGER_PASSENGER_DOOR      0x05        // 副驾车门
#define TRIGGER_REAR_LEFT_DOOR      0x06        // 左后车门
#define TRIGGER_REAR_RIGHT_DOOR     0x07        // 右后车门
#define TRIGGER_DOOR_WARNING        0x08        // 车门未关警告
#define TRIGGER_PARKING_REMIND      0x09        // 停车未熄火提醒
#define TRIGGER_STEERING_WARNING    0x0A        // 方向盘预警
#define TRIGGER_FATIGUE_DRIVING     0x0B        // 疲劳驾驶提醒
#define TRIGGER_TAKE_ITEMS          0x0C        // 熄火物品提醒
#define TRIGGER_STEERING_CENTER     0x0D        // 方向盘未回正
#define TRIGGER_TURN_SIGNAL_LONG    0x0E        // 转向灯持续过长

// 优先级定义
#define PRIORITY_LOW                0           // 低优先级
#define PRIORITY_NORMAL             1           // 普通优先级
#define PRIORITY_HIGH               2           // 高优先级
#define PRIORITY_CRITICAL           3           // 紧急优先级

// 汽车语音优先级定义（兼容性）
#define CAR_VOICE_PRIORITY_LOW      PRIORITY_LOW
#define CAR_VOICE_PRIORITY_NORMAL   PRIORITY_NORMAL
#define CAR_VOICE_PRIORITY_HIGH     PRIORITY_HIGH
#define CAR_VOICE_PRIORITY_CRITICAL PRIORITY_CRITICAL

// 汽车数据包结构
typedef struct {
    uint8_t header;                             // 数据包头
    uint8_t data_type;                          // 数据类型
    uint8_t length;                             // 数据长度
    uint8_t payload[64];                        // 数据内容
    uint8_t checksum;                           // 校验和
} car_data_packet_t;

// 车门状态结构
typedef struct {
    bool driver_door;                           // 主驾车门
    bool passenger_door;                        // 副驾车门
    bool rear_left_door;                        // 左后门
    bool rear_right_door;                       // 右后门
} car_doors_t;

// 转向灯状态结构
typedef struct {
    bool left_signal;                           // 左转向灯
    bool right_signal;                          // 右转向灯
} car_turn_signals_t;

// 车辆状态结构
typedef struct {
    uint8_t acc_status;                         // ACC状态 (0=关闭, 1=开启)
    uint8_t gear_position;                      // 档位 (0=P, 1=R, 2=N, 3=D)
    uint16_t vehicle_speed;                     // 车速 (km/h)
    car_doors_t doors;                          // 车门状态
    car_turn_signals_t turn_signals;            // 转向灯状态
    uint8_t steering_angle;                     // 方向盘角度 (0-180度)
    uint32_t last_update_time;                  // 最后更新时间 (ms)
    bool data_valid;                            // 数据有效性
} vehicle_state_t;

// 语音触发规则结构
typedef struct {
    uint8_t voice_id;                           // 语音ID (1-17)
    uint8_t trigger_condition;                  // 触发条件
    uint8_t priority;                           // 优先级 (0-3)
    uint32_t cooldown_ms;                       // 冷却时间 (ms)
    uint32_t last_trigger_time;                 // 上次触发时间
    bool (*check_condition)(const vehicle_state_t* current, const vehicle_state_t* previous);
} voice_trigger_rule_t;

// 语音播放请求结构
typedef struct {
    uint8_t voice_id;                           // 语音ID
    uint8_t priority;                           // 优先级
    uint32_t timestamp;                         // 时间戳
} voice_play_request_t;

// 系统状态结构
typedef struct {
    bool initialized;                           // 初始化状态
    bool uart_enabled;                          // UART启用状态
    bool voice_enabled;                         // 语音播放启用状态
    uint32_t data_received_count;               // 接收数据包计数
    uint32_t voice_played_count;                // 播放语音计数
    uint32_t last_data_time;                    // 最后接收数据时间
} car_voice_system_status_t;

// 汽车语音触发状态结构（兼容性）
typedef struct {
    bool enabled;                               // 系统启用状态
    bool voice_enabled;                         // 语音播放启用状态
    uint32_t packets_received;                  // 接收数据包计数
    uint32_t voice_played_count;                // 播放语音计数
    uint32_t parse_errors;                      // 解析错误计数
    uint32_t last_update_time;                  // 最后更新时间
    uint8_t protocol_type;                      // 协议类型
    vehicle_state_t vehicle_state;              // 车辆状态
} car_voice_trigger_status_t;

// 主要接口函数
esp_err_t car_voice_trigger_init(void);
esp_err_t car_voice_trigger_deinit(void);
esp_err_t car_voice_trigger_start(void);
esp_err_t car_voice_trigger_stop(void);

// 配置接口
esp_err_t car_voice_set_enabled(bool enabled);
bool car_voice_is_enabled(void);
esp_err_t car_voice_set_uart_enabled(bool enabled);
bool car_voice_is_uart_enabled(void);

// 状态查询接口
car_voice_system_status_t car_voice_get_status(void);
vehicle_state_t car_voice_get_vehicle_state(void);
esp_err_t car_voice_trigger_get_status(car_voice_trigger_status_t* status);

// 测试接口
esp_err_t car_voice_test_play(uint8_t voice_id);
esp_err_t car_voice_test_data_inject(const uint8_t* data, size_t len);
esp_err_t car_voice_trigger_play_voice(uint8_t voice_id, uint8_t priority);
esp_err_t car_voice_trigger_inject_data(const uint8_t* data, size_t len);
esp_err_t car_voice_trigger_enable(bool enabled);

// 调试接口
void car_voice_print_status(void);
void car_voice_print_vehicle_state(void);



#ifdef __cplusplus
}
#endif

#endif // CAR_VOICE_TRIGGER_H
