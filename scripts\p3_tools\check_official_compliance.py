#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查P3文件是否符合官方README.md中的格式规范
"""

import struct
import os
import sys

def check_official_p3_compliance(file_path):
    """
    根据官方README.md检查P3文件的符合性
    
    官方规范:
    - 每个音频帧由一个4字节的头部和一个Opus编码的数据包组成
    - 头部格式：[1字节类型, 1字节保留, 2字节长度]
    - 采样率固定为16000Hz，单声道
    - 每帧时长为60ms
    """
    
    print(f"🔍 检查P3文件官方规范符合性: {file_path}")
    print("=" * 70)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"📁 文件大小: {file_size:,} 字节")
    
    compliance_score = 0
    total_checks = 7
    issues = []
    
    packets = []
    
    with open(file_path, 'rb') as f:
        packet_index = 0
        
        while True:
            # 检查1: 4字节头部结构
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            if len(header) == 4:
                compliance_score += 0.1  # 每个正确的头部加分
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            # 读取数据包
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                issues.append(f"数据包{packet_index+1}: 数据长度不匹配")
                break
            
            packets.append({
                'index': packet_index,
                'packet_type': packet_type,
                'reserved': reserved,
                'data_len': data_len,
                'data': packet_data
            })
            
            packet_index += 1
    
    print(f"📦 总数据包数: {len(packets)}")
    
    # 检查1: 头部格式 [1字节类型, 1字节保留, 2字节长度]
    print(f"\n1️⃣  头部格式检查: [1字节类型, 1字节保留, 2字节长度]")
    
    # 检查类型字节
    packet_types = [p['packet_type'] for p in packets]
    unique_types = set(packet_types)
    
    if len(unique_types) == 1 and 0 in unique_types:
        print(f"   ✅ 类型字节: 全部为0 (音频数据)")
        compliance_score += 1
    else:
        print(f"   ❌ 类型字节: {unique_types} (应该全部为0)")
        issues.append("类型字节不符合规范")
    
    # 检查保留字节
    reserved_bytes = [p['reserved'] for p in packets]
    unique_reserved = set(reserved_bytes)
    
    if len(unique_reserved) == 1 and 0 in unique_reserved:
        print(f"   ✅ 保留字节: 全部为0")
        compliance_score += 1
    else:
        print(f"   ❌ 保留字节: {unique_reserved} (应该全部为0)")
        issues.append("保留字节不符合规范")
    
    # 检查长度字段
    length_errors = sum(1 for p in packets if p['data_len'] != len(p['data']))
    if length_errors == 0:
        print(f"   ✅ 长度字段: 完全准确")
        compliance_score += 1
    else:
        print(f"   ❌ 长度字段: {length_errors}个错误")
        issues.append("长度字段不准确")
    
    # 检查2: Opus编码数据包
    print(f"\n2️⃣  Opus编码检查")
    
    opus_packets = [p for p in packets if len(p['data']) > 0]
    
    if opus_packets:
        # 检查TOC字节
        toc_bytes = [p['data'][0] for p in opus_packets]
        unique_tocs = set(toc_bytes)
        
        print(f"   TOC字节: {[f'0x{b:02X}' for b in unique_tocs]}")
        
        # 解析第一个TOC字节
        if opus_packets:
            first_toc = opus_packets[0]['data'][0]
            config = (first_toc >> 3) & 0x1F
            stereo = (first_toc >> 2) & 0x01
            frame_count = first_toc & 0x03
            
            # 解析配置
            if config == 11:  # SILK WB 60ms
                print(f"   ✅ Opus配置: Config 11 (SILK WB 60ms)")
                compliance_score += 1
            else:
                print(f"   ❌ Opus配置: Config {config} (应该是11)")
                issues.append("Opus配置不符合预期")
            
            if stereo == 0:
                print(f"   ✅ 声道配置: 单声道")
                compliance_score += 1
            else:
                print(f"   ❌ 声道配置: 立体声 (应该是单声道)")
                issues.append("声道配置不符合规范")
    
    # 检查3: 采样率和帧时长推断
    print(f"\n3️⃣  采样率和帧时长检查")
    
    if len(packets) > 0:
        # 根据数据包数量和总时长推断
        # 假设这是合理长度的音频
        estimated_duration = len(packets) * 0.06  # 60ms per frame
        print(f"   估计播放时长: {estimated_duration:.2f} 秒 (基于60ms/帧)")
        
        # 检查是否符合60ms帧时长
        if all(0.05 <= 0.06 <= 0.07 for _ in [0.06]):  # 60ms在合理范围内
            print(f"   ✅ 帧时长: 60ms (符合规范)")
            compliance_score += 1
        else:
            print(f"   ❌ 帧时长: 不符合60ms规范")
            issues.append("帧时长不符合规范")
    
    # 检查4: 数据包大小合理性
    print(f"\n4️⃣  数据包大小检查")
    
    if packets:
        sizes = [p['data_len'] for p in packets]
        min_size, max_size = min(sizes), max(sizes)
        avg_size = sum(sizes) / len(sizes)
        
        print(f"   数据包大小范围: {min_size}-{max_size} 字节")
        print(f"   平均大小: {avg_size:.1f} 字节")
        
        # Opus数据包通常在20-200字节范围内
        if 20 <= min_size and max_size <= 500:
            print(f"   ✅ 大小范围: 合理 (20-500字节)")
            compliance_score += 1
        else:
            print(f"   ❌ 大小范围: 不合理")
            issues.append("数据包大小不合理")
    
    # 生成符合性报告
    print(f"\n" + "=" * 70)
    print(f"📋 官方规范符合性报告")
    print(f"=" * 70)
    
    compliance_percentage = (compliance_score / total_checks) * 100
    
    print(f"🎯 符合性得分: {compliance_score:.1f}/{total_checks} ({compliance_percentage:.1f}%)")
    
    if compliance_percentage >= 95:
        print(f"🎉 完全符合官方规范！")
        result = "FULLY_COMPLIANT"
    elif compliance_percentage >= 85:
        print(f"✅ 基本符合官方规范！")
        result = "MOSTLY_COMPLIANT"
    elif compliance_percentage >= 70:
        print(f"⚠️  部分符合官方规范")
        result = "PARTIALLY_COMPLIANT"
    else:
        print(f"❌ 不符合官方规范")
        result = "NON_COMPLIANT"
    
    if issues:
        print(f"\n⚠️  发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    else:
        print(f"\n✅ 未发现规范问题")
    
    print(f"\n📖 官方规范要求:")
    print(f"   - 4字节头部: [类型, 保留, 长度(2字节)]")
    print(f"   - Opus编码数据包")
    print(f"   - 16000Hz采样率, 单声道")
    print(f"   - 60ms帧时长")
    
    print(f"=" * 70)
    
    return result

def main():
    if len(sys.argv) != 2:
        print("使用方法: python check_official_compliance.py <p3文件>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    result = check_official_p3_compliance(file_path)
    
    # 返回适当的退出码
    exit_codes = {
        "FULLY_COMPLIANT": 0,
        "MOSTLY_COMPLIANT": 0,
        "PARTIALLY_COMPLIANT": 1,
        "NON_COMPLIANT": 2
    }
    
    sys.exit(exit_codes.get(result, 2))

if __name__ == "__main__":
    main()
