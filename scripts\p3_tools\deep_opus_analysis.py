#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析999.p3中的真实Opus数据包内容
找出我们转换器的根本问题
"""

import struct
import os
import sys
import numpy as np

def deep_analyze_opus_packets(file_path):
    """
    深度分析Opus数据包的内部结构
    """
    print(f"🔬 深度分析Opus数据包: {file_path}")
    print("=" * 70)
    
    packets = []
    
    with open(file_path, 'rb') as f:
        packet_index = 0
        while True:
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
            
            packets.append({
                'index': packet_index,
                'size': data_len,
                'data': packet_data
            })
            
            packet_index += 1
    
    print(f"📦 总数据包数: {len(packets)}")
    
    # 分析每个数据包的详细结构
    analyze_packet_structure(packets)
    
    # 分析数据包的内容模式
    analyze_content_patterns(packets)
    
    # 分析Opus特定特征
    analyze_opus_features(packets)
    
    # 尝试识别真实vs模拟数据
    detect_real_vs_simulated(packets)

def analyze_packet_structure(packets):
    """
    分析数据包结构
    """
    print(f"\n📋 数据包结构分析:")
    print("-" * 50)
    
    for i, packet in enumerate(packets[:5]):  # 分析前5个
        data = packet['data']
        size = packet['size']
        
        print(f"\n数据包 {i+1} (大小: {size} 字节):")
        
        if len(data) > 0:
            # TOC字节分析
            toc = data[0]
            config = (toc >> 3) & 0x1F
            stereo = (toc >> 2) & 0x01
            frame_count = toc & 0x03
            
            print(f"  TOC: 0x{toc:02X} (config={config}, stereo={stereo}, frames={frame_count})")
            
            # 显示前32字节的十六进制
            hex_data = data[:32].hex()
            print(f"  前32字节: {hex_data}")
            
            # 字节值分析
            if len(data) > 1:
                byte_values = list(data[1:min(17, len(data))])  # 跳过TOC，分析接下来16字节
                print(f"  字节1-16: {[f'0x{b:02X}' for b in byte_values]}")
                
                # 统计特征
                avg_val = np.mean(byte_values)
                std_val = np.std(byte_values)
                print(f"  统计: 平均=0x{int(avg_val):02X}, 标准差={std_val:.1f}")

def analyze_content_patterns(packets):
    """
    分析内容模式
    """
    print(f"\n🔍 内容模式分析:")
    print("-" * 50)
    
    # 收集所有字节（跳过TOC字节）
    all_bytes = []
    for packet in packets:
        if len(packet['data']) > 1:
            all_bytes.extend(packet['data'][1:])  # 跳过TOC字节
    
    if not all_bytes:
        print("没有足够的数据进行分析")
        return
    
    # 字节频率分析
    byte_freq = np.bincount(all_bytes, minlength=256)
    
    # 最常见的字节
    top_bytes = sorted(enumerate(byte_freq), key=lambda x: x[1], reverse=True)[:10]
    print(f"最常见字节 (前10个):")
    for byte_val, count in top_bytes:
        percentage = (count / len(all_bytes)) * 100
        print(f"  0x{byte_val:02X}: {count} 次 ({percentage:.1f}%)")
    
    # 熵分析
    byte_probs = byte_freq / np.sum(byte_freq)
    entropy = -np.sum(byte_probs * np.log2(byte_probs + 1e-10))
    print(f"\n信息熵: {entropy:.3f} bits/byte (最大8.0)")
    
    # 连续性分析
    consecutive_patterns = find_consecutive_bytes(all_bytes)
    if consecutive_patterns:
        print(f"\n连续字节模式:")
        for pattern, count in consecutive_patterns[:5]:
            print(f"  {pattern}: {count} 次")

def find_consecutive_bytes(data):
    """
    查找连续字节模式
    """
    patterns = {}
    
    i = 0
    while i < len(data) - 1:
        if data[i] == data[i+1]:
            # 找到连续字节
            byte_val = data[i]
            count = 2
            j = i + 2
            while j < len(data) and data[j] == byte_val:
                count += 1
                j += 1
            
            pattern = f"{count}x0x{byte_val:02X}"
            patterns[pattern] = patterns.get(pattern, 0) + 1
            i = j
        else:
            i += 1
    
    return sorted(patterns.items(), key=lambda x: x[1], reverse=True)

def analyze_opus_features(packets):
    """
    分析Opus特定特征
    """
    print(f"\n🎵 Opus特征分析:")
    print("-" * 50)
    
    # 分析第二字节模式（Opus帧头的一部分）
    second_bytes = []
    for packet in packets:
        if len(packet['data']) > 1:
            second_bytes.append(packet['data'][1])
    
    if second_bytes:
        unique_second = set(second_bytes)
        print(f"第二字节唯一值: {[f'0x{b:02X}' for b in sorted(unique_second)]}")
        print(f"第二字节序列: {[f'0x{b:02X}' for b in second_bytes]}")
        
        # 检查是否有规律
        if len(unique_second) == 1:
            print("  ✅ 第二字节固定")
        elif len(unique_second) < len(second_bytes) / 2:
            print("  ⚠️  第二字节有限变化")
        else:
            print("  ❌ 第二字节高度变化")
    
    # 分析数据包大小模式
    sizes = [p['size'] for p in packets]
    print(f"\n数据包大小序列: {sizes}")
    
    # 检查大小变化模式
    if len(sizes) > 1:
        diffs = [sizes[i+1] - sizes[i] for i in range(len(sizes)-1)]
        print(f"大小变化序列: {diffs}")
        
        # 检查是否有周期性
        if len(set(diffs)) < len(diffs) / 2:
            print("  ⚠️  大小变化有模式")
        else:
            print("  ✅ 大小变化随机")

def detect_real_vs_simulated(packets):
    """
    尝试检测真实vs模拟的Opus数据
    """
    print(f"\n🕵️  真实性检测:")
    print("-" * 50)
    
    indicators = []
    
    # 检查1: 字节分布的随机性
    all_bytes = []
    for packet in packets:
        if len(packet['data']) > 1:
            all_bytes.extend(packet['data'][1:])
    
    if all_bytes:
        # 卡方检验 - 检查是否接近均匀分布
        expected_freq = len(all_bytes) / 256
        byte_freq = np.bincount(all_bytes, minlength=256)
        chi_square = np.sum((byte_freq - expected_freq) ** 2 / expected_freq)
        
        print(f"字节分布卡方值: {chi_square:.1f}")
        if chi_square > 300:  # 阈值需要调整
            indicators.append("字节分布非随机")
            print("  ❌ 字节分布过于规律，可能是模拟数据")
        else:
            indicators.append("字节分布较随机")
            print("  ✅ 字节分布较随机，可能是真实数据")
    
    # 检查2: 数据包间的相关性
    if len(packets) > 1:
        correlations = []
        for i in range(min(5, len(packets)-1)):
            data1 = packets[i]['data'][1:min(33, len(packets[i]['data']))]
            data2 = packets[i+1]['data'][1:min(33, len(packets[i+1]['data']))]
            
            if len(data1) == len(data2) and len(data1) > 0:
                corr = np.corrcoef(data1, data2)[0, 1]
                if not np.isnan(corr):
                    correlations.append(abs(corr))
        
        if correlations:
            avg_corr = np.mean(correlations)
            print(f"数据包间平均相关性: {avg_corr:.3f}")
            
            if avg_corr > 0.3:
                indicators.append("数据包间相关性高")
                print("  ❌ 数据包间相关性过高，可能是模拟数据")
            else:
                indicators.append("数据包间相关性低")
                print("  ✅ 数据包间相关性低，可能是真实数据")
    
    # 检查3: Opus特定模式
    # 真实的Opus数据通常在某些位置有特定的模式
    opus_patterns = check_opus_patterns(packets)
    indicators.extend(opus_patterns)
    
    # 综合判断
    print(f"\n🎯 综合判断:")
    real_indicators = sum(1 for ind in indicators if "真实" in ind or "随机" in ind or "低" in ind)
    fake_indicators = sum(1 for ind in indicators if "模拟" in ind or "规律" in ind or "高" in ind)
    
    if real_indicators > fake_indicators:
        print("✅ 可能是真实的Opus编码数据")
    elif fake_indicators > real_indicators:
        print("❌ 可能是模拟/生成的数据")
    else:
        print("⚠️  无法确定数据真实性")

def check_opus_patterns(packets):
    """
    检查Opus特定模式
    """
    patterns = []
    
    # 检查TOC字节一致性
    toc_bytes = [p['data'][0] for p in packets if len(p['data']) > 0]
    if len(set(toc_bytes)) == 1:
        patterns.append("TOC字节一致")
    
    # 检查是否有Opus特定的字节序列
    for packet in packets[:3]:
        data = packet['data']
        if len(data) > 10:
            # 检查是否有典型的Opus编码模式
            # 这需要对Opus内部格式的深入了解
            pass
    
    return patterns

def main():
    if len(sys.argv) != 2:
        print("使用方法: python deep_opus_analysis.py <p3文件>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        sys.exit(1)
    
    deep_analyze_opus_packets(file_path)

if __name__ == "__main__":
    main()
