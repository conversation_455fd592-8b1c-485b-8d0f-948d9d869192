#pragma once

#include <string>
#include <map>
#include <vector>
#include "esp_err.h"
#include "nvs_flash.h"
#include "nvs.h"

/**
 * @brief 文本指令存储管理器
 * 
 * 负责动态文本指令的NVS存储管理，包括增删改查操作
 */
class TextCommandStorage {
public:
    /**
     * @brief 构造函数
     */
    TextCommandStorage();

    /**
     * @brief 析构函数
     */
    ~TextCommandStorage();

    /**
     * @brief 初始化存储系统
     * @return esp_err_t 初始化结果
     */
    esp_err_t Initialize();

    /**
     * @brief 添加文本指令
     * @param name 指令名称（英文）
     * @param text 指令内容（中文）
     * @return esp_err_t 操作结果
     */
    esp_err_t AddCommand(const std::string& name, const std::string& text);

    /**
     * @brief 修改文本指令
     * @param name 指令名称
     * @param new_text 新的指令内容
     * @return esp_err_t 操作结果
     */
    esp_err_t ModifyCommand(const std::string& name, const std::string& new_text);

    /**
     * @brief 删除文本指令
     * @param name 指令名称
     * @return esp_err_t 操作结果
     */
    esp_err_t DeleteCommand(const std::string& name);

    /**
     * @brief 获取指令内容
     * @param name 指令名称
     * @return std::string 指令内容，如果不存在返回空字符串
     */
    std::string GetCommand(const std::string& name);

    /**
     * @brief 获取所有指令
     * @return std::map<std::string, std::string> 所有指令的映射
     */
    std::map<std::string, std::string> GetAllCommands();

    /**
     * @brief 清空所有自定义指令
     * @return esp_err_t 操作结果
     */
    esp_err_t ClearAllCommands();

    /**
     * @brief 批量导入指令
     * @param commands 指令映射
     * @return esp_err_t 操作结果
     */
    esp_err_t ImportCommands(const std::map<std::string, std::string>& commands);

    /**
     * @brief 获取指令数量
     * @return size_t 指令数量
     */
    size_t GetCommandCount();

    /**
     * @brief 检查指令是否存在
     * @param name 指令名称
     * @return bool 是否存在
     */
    bool CommandExists(const std::string& name);

    /**
     * @brief 验证指令名称是否有效
     * @param name 指令名称
     * @return bool 是否有效
     */
    static bool IsValidCommandName(const std::string& name);

    /**
     * @brief 验证指令内容是否有效
     * @param text 指令内容
     * @return bool 是否有效
     */
    static bool IsValidCommandText(const std::string& text);

private:
    nvs_handle_t nvs_handle_;
    bool initialized_;

    static const char* NVS_NAMESPACE;
    static const char* KEY_PREFIX;
    static const char* COUNT_KEY;
    static const size_t MAX_COMMAND_NAME_LENGTH;
    static const size_t MAX_COMMAND_TEXT_LENGTH;
    static const size_t MAX_COMMAND_COUNT;

    /**
     * @brief 生成NVS键名
     * @param name 指令名称
     * @return std::string NVS键名
     */
    std::string GenerateKey(const std::string& name);

    /**
     * @brief 更新指令计数
     * @return esp_err_t 操作结果
     */
    esp_err_t UpdateCommandCount();

    /**
     * @brief 获取存储的指令计数
     * @return size_t 指令计数
     */
    size_t GetStoredCommandCount();
};
