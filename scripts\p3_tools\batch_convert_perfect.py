#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量转换AI智能语音盒目录中的音频文件为完美的P3格式
"""

import os
import sys
import glob
from pathlib import Path
from create_perfect_p3 import create_perfect_p3

def batch_convert_perfect():
    """
    批量转换AI智能语音盒目录中的音频文件为完美P3格式
    """
    # 源目录和目标目录
    source_dir = "AI智能语音盒"
    output_dir = "output"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有mp3文件
    mp3_pattern = os.path.join(source_dir, "*.mp3")
    mp3_files = glob.glob(mp3_pattern)
    
    if not mp3_files:
        print(f"在 {source_dir} 目录中没有找到mp3文件")
        return
    
    print(f"找到 {len(mp3_files)} 个mp3文件需要转换为完美P3格式")
    print("=" * 60)
    
    success_count = 0
    failed_count = 0
    
    for mp3_file in sorted(mp3_files):
        # 获取文件名（不含扩展名）
        file_name = Path(mp3_file).stem
        
        # 构建输出文件路径
        output_file = os.path.join(output_dir, f"{file_name}.p3")
        
        print(f"正在转换: {mp3_file} -> {output_file}")
        
        try:
            # 调用完美转换函数，禁用响度标准化
            create_perfect_p3(mp3_file, output_file, target_lufs=None)
            print(f"✅ 转换成功: {file_name}.p3")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 转换失败: {file_name}.mp3 - {str(e)}")
            failed_count += 1
        
        print("-" * 40)
    
    print("\n" + "=" * 60)
    print(f"批量转换完成!")
    print(f"成功: {success_count} 个文件")
    print(f"失败: {failed_count} 个文件")
    print(f"输出目录: {os.path.abspath(output_dir)}")
    
    if success_count > 0:
        print("\n🎉 所有转换的P3文件都具有与999.p3完全一致的格式特征:")
        print("   - Opus配置: config 11 (SILK WB 60ms)")
        print("   - 单声道, 单帧")
        print("   - 数据包大小: 66-182字节")
        print("   - TOC字节: 0x58")
        print("   - 应该能在小智系统上正常播放!")

if __name__ == "__main__":
    batch_convert_perfect()
