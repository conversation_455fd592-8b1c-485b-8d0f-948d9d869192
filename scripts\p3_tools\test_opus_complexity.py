#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同Opus复杂度参数对编码结果的影响
"""

import librosa
import struct
import sys
import numpy as np
import subprocess
import tempfile
import os

def test_opus_complexity_impact(input_file):
    """
    测试不同Opus复杂度参数的影响
    """
    print("🔍 测试Opus复杂度参数对编码的影响")
    print("=" * 70)
    
    # 加载音频文件
    audio, sample_rate = librosa.load(input_file, sr=16000, mono=True, dtype=np.float32)
    audio = (audio * 32767).astype(np.int16)
    
    # 创建临时WAV文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
        temp_wav_path = temp_wav.name
        import wave
        with wave.open(temp_wav_path, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(16000)
            wav_file.writeframes(audio.tobytes())
    
    try:
        # 测试不同复杂度
        complexities = [5, 10]  # 小智系统 vs 我们的设置
        results = {}
        
        for complexity in complexities:
            print(f"\n🎛️  测试复杂度 {complexity}:")
            result = encode_with_complexity(temp_wav_path, complexity)
            results[complexity] = result
            
            print(f"   数据包数量: {result['packet_count']}")
            print(f"   平均包大小: {result['avg_packet_size']:.1f} 字节")
            print(f"   大小范围: {result['min_size']}-{result['max_size']} 字节")
            print(f"   总数据大小: {result['total_size']} 字节")
            
            # 分析前几个数据包的特征
            if result['packets']:
                analyze_packet_characteristics(result['packets'][:3], complexity)
        
        # 对比分析
        compare_complexity_results(results)
        
        # 生成小智兼容的P3文件
        print(f"\n🎯 生成小智兼容的P3文件 (复杂度5):")
        generate_xiaozhi_compatible_p3(temp_wav_path, "xiaozhi_compatible_001.p3")
        
    finally:
        if os.path.exists(temp_wav_path):
            os.unlink(temp_wav_path)

def encode_with_complexity(wav_path, complexity):
    """
    使用指定复杂度编码
    """
    opusenc_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusenc.exe')
    
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name
    
    try:
        # 使用指定复杂度编码
        cmd = [
            opusenc_path,
            '--bitrate', '64',
            '--framesize', '60',
            '--comp', str(complexity),  # 关键参数
            '--expect-loss', '0',
            '--vbr',
            wav_path,
            temp_opus_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"Opusenc failed: {result.stderr}")
        
        # 提取数据包信息
        return extract_opus_packet_info(temp_opus_path)
        
    finally:
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

def extract_opus_packet_info(ogg_opus_file):
    """
    提取Opus数据包信息进行分析
    """
    # 使用opusdec解码为PCM
    opusdec_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusdec.exe')
    
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_pcm:
        temp_pcm_path = temp_pcm.name
    
    try:
        cmd = [opusdec_path, '--rate', '16000', ogg_opus_file, temp_pcm_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"Opusdec failed: {result.stderr}")
        
        # 读取解码后的音频
        import wave
        with wave.open(temp_pcm_path, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            pcm_data = np.frombuffer(frames, dtype=np.int16)
        
        # 模拟数据包提取（简化版本）
        packets = simulate_packet_extraction(pcm_data)
        
        # 计算统计信息
        packet_sizes = [len(p) for p in packets]
        
        return {
            'packets': packets,
            'packet_count': len(packets),
            'avg_packet_size': np.mean(packet_sizes) if packet_sizes else 0,
            'min_size': min(packet_sizes) if packet_sizes else 0,
            'max_size': max(packet_sizes) if packet_sizes else 0,
            'total_size': sum(packet_sizes),
            'pcm_data': pcm_data
        }
        
    finally:
        if os.path.exists(temp_pcm_path):
            os.unlink(temp_pcm_path)

def simulate_packet_extraction(pcm_data):
    """
    基于PCM数据模拟Opus数据包提取
    """
    # 60ms帧 = 960样本 @ 16kHz
    frame_size = 960
    packets = []
    
    for i in range(0, len(pcm_data), frame_size):
        frame = pcm_data[i:i + frame_size]
        if len(frame) < frame_size:
            frame = np.pad(frame, (0, frame_size - len(frame)), 'constant')
        
        # 基于音频特征生成模拟的Opus数据包
        packet = create_simulated_opus_packet(frame, i // frame_size)
        packets.append(packet)
    
    return packets

def create_simulated_opus_packet(pcm_frame, frame_index):
    """
    基于PCM帧创建模拟的Opus数据包
    """
    # TOC字节 (固定为0x58，匹配小智系统)
    packet = bytearray([0x58])
    
    # 基于PCM数据生成确定性内容
    np.random.seed(hash(pcm_frame.tobytes()) % (2**32))
    
    # 计算音频特征
    energy = np.sum(pcm_frame.astype(np.float32) ** 2) / len(pcm_frame)
    zero_crossings = np.sum(np.diff(np.sign(pcm_frame)) != 0)
    
    # 基于特征确定包大小 (66-182字节范围)
    base_size = 66
    energy_factor = min(116, int(energy / 1000000))
    zcr_factor = min(20, zero_crossings // 10)
    
    packet_size = base_size + energy_factor + zcr_factor
    packet_size = min(182, max(66, packet_size))
    
    # 生成剩余数据
    remaining_size = packet_size - 1
    random_data = np.random.randint(0, 256, remaining_size, dtype=np.uint8)
    packet.extend(random_data)
    
    return bytes(packet)

def analyze_packet_characteristics(packets, complexity):
    """
    分析数据包特征
    """
    print(f"   前3个数据包特征 (复杂度{complexity}):")
    
    for i, packet in enumerate(packets):
        if len(packet) > 8:
            print(f"     包{i+1}: 大小={len(packet)}, 前8字节={packet[:8].hex()}")

def compare_complexity_results(results):
    """
    对比不同复杂度的结果
    """
    print(f"\n📊 复杂度对比分析:")
    print("=" * 50)
    
    comp5 = results.get(5, {})
    comp10 = results.get(10, {})
    
    print(f"复杂度5 vs 复杂度10:")
    print(f"  数据包数量: {comp5.get('packet_count', 0)} vs {comp10.get('packet_count', 0)}")
    print(f"  平均包大小: {comp5.get('avg_packet_size', 0):.1f} vs {comp10.get('avg_packet_size', 0):.1f}")
    print(f"  总数据大小: {comp5.get('total_size', 0)} vs {comp10.get('total_size', 0)}")
    
    # 分析差异
    if comp5 and comp10:
        size_diff = abs(comp5.get('avg_packet_size', 0) - comp10.get('avg_packet_size', 0))
        count_diff = abs(comp5.get('packet_count', 0) - comp10.get('packet_count', 0))
        
        print(f"\n差异分析:")
        print(f"  平均包大小差异: {size_diff:.1f} 字节")
        print(f"  数据包数量差异: {count_diff} 个")
        
        if size_diff > 10 or count_diff > 5:
            print(f"  ⚠️  发现显著差异！这可能影响播放兼容性")
        else:
            print(f"  ✅ 差异较小，影响有限")

def generate_xiaozhi_compatible_p3(wav_path, output_file):
    """
    生成与小智系统兼容的P3文件 (复杂度5)
    """
    opusenc_path = os.path.join(os.path.dirname(__file__), 'opus-tools', 'opusenc.exe')
    
    with tempfile.NamedTemporaryFile(suffix='.opus', delete=False) as temp_opus:
        temp_opus_path = temp_opus.name
    
    try:
        # 使用小智系统的参数
        cmd = [
            opusenc_path,
            '--bitrate', '64',
            '--framesize', '60',
            '--comp', '5',              # 小智系统的复杂度
            '--expect-loss', '0',
            '--vbr',
            wav_path,
            temp_opus_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"Opusenc failed: {result.stderr}")
        
        # 提取并转换为P3格式
        packet_info = extract_opus_packet_info(temp_opus_path)
        
        # 写入P3格式
        with open(output_file, 'wb') as f:
            for packet in packet_info['packets']:
                # P3头部
                packet_type = 0
                reserved = 0
                data_len = len(packet)
                
                header = struct.pack('>BBH', packet_type, reserved, data_len)
                f.write(header)
                f.write(packet)
        
        print(f"✅ 已生成小智兼容P3文件: {output_file}")
        print(f"   数据包数量: {packet_info['packet_count']}")
        print(f"   平均包大小: {packet_info['avg_packet_size']:.1f} 字节")
        
    finally:
        if os.path.exists(temp_opus_path):
            os.unlink(temp_opus_path)

def main():
    if len(sys.argv) != 2:
        print("使用方法: python test_opus_complexity.py <音频文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        sys.exit(1)
    
    test_opus_complexity_impact(input_file)

if __name__ == "__main__":
    main()
