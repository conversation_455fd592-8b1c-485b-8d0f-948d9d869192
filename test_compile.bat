@echo off
echo Starting test compilation...
cd /d "J:\xiaozhi-esp32"

echo Setting ESP-IDF environment...
set IDF_PATH=D:\Espressif\frameworks\esp-idf-v5.4.1
set PATH=D:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin;D:\Espressif\tools\cmake\3.30.2\bin;D:\Espressif\tools\ninja\1.12.1;D:\Espressif\python_env\idf5.4_py3.11_env\Scripts;%PATH%

echo Starting build...
D:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe D:\Espressif\frameworks\esp-idf-v5.4.1\tools\idf.py build

echo Build completed with exit code: %ERRORLEVEL%
pause
