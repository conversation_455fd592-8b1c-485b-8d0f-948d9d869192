#include "uart_command_manager.h"
#include "text_interaction/text_commands.h"
#include "network_console.h"
#include "esp_log.h"
#include "system_info.h"
#include <sstream>
#include <algorithm>

static const char* TAG = "UartCommandManager";

namespace communication {

UartCommandManager::UartCommandManager() 
    : storage_(nullptr), text_commands_(nullptr), network_console_(nullptr), 
      initialized_(false) {
    storage_ = new TextCommandStorage();
}

UartCommandManager::~UartCommandManager() {
    if (storage_) {
        delete storage_;
    }
}

esp_err_t UartCommandManager::Initialize(text_interaction::TextCommands* text_commands, NetworkConsole* network_console) {
    if (initialized_) {
        return ESP_OK;
    }

    if (!network_console) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }

    text_commands_ = text_commands;  // 可以为nullptr，因为TextCommands是静态类
    network_console_ = network_console;

    esp_err_t err = storage_->Initialize();
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize storage: %s", esp_err_to_name(err));
        return err;
    }

    // 添加预设的文本指令
    InitializeDefaultTextCommands();

    initialized_ = true;
    ESP_LOGI(TAG, "UART command manager initialized successfully");
    return ESP_OK;
}

void UartCommandManager::InitializeDefaultTextCommands() {
    ESP_LOGI(TAG, "Initializing default text commands...");

    // 检查是否已有文本指令，如果没有则添加预设指令
    auto commands = storage_->GetAllCommands();
    if (commands.empty()) {
        ESP_LOGI(TAG, "No existing text commands found, adding default commands");

        // 添加预设的文本指令
        storage_->AddCommand("ask", "发送任意文本给小智AI");
        storage_->AddCommand("now", "按照以下格式播报今日完整信息：今天是****年*月**日周*，农历*月初*。（ip地址天气：）阴天，28℃，西北风1级，空气质量优质。今天白天有中雨，记得带伞哦~");
        storage_->AddCommand("time", "现在几点了");
        storage_->AddCommand("weather", "今天天气怎么样");
        storage_->AddCommand("hello", "你好小智");
        storage_->AddCommand("joke", "给我讲个笑话");

        ESP_LOGI(TAG, "Added 6 default text commands");
    } else {
        ESP_LOGI(TAG, "Found %d existing text commands, skipping default initialization", commands.size());
    }
}

void UartCommandManager::ProcessReceivedData(const std::string& data) {
    if (!initialized_) {
        ESP_LOGW(TAG, "Manager not initialized");
        return;
    }

    ESP_LOGI(TAG, "📥 Received %d bytes from PC client", data.length());
    receive_buffer_ += data;

    // 处理完整的命令行
    size_t pos;
    while ((pos = receive_buffer_.find('\n')) != std::string::npos) {
        std::string line = receive_buffer_.substr(0, pos);
        receive_buffer_ = receive_buffer_.substr(pos + 1);

        // 移除回车符
        if (!line.empty() && line.back() == '\r') {
            line.pop_back();
        }

        // 处理非空行
        if (!line.empty()) {
            ESP_LOGI(TAG, "🔍 Processing command line: '%s'", line.c_str());
            ProcessCommandLine(line);
        }
    }
}

void UartCommandManager::SetResponseCallback(std::function<void(const std::string&)> callback) {
    response_callback_ = callback;
}

void UartCommandManager::ProcessCommandLine(const std::string& line) {
    std::vector<std::string> parts = SplitString(line, ':');
    if (parts.empty()) {
        SendErrorResponse("invalid command format");
        return;
    }

    std::string command = parts[0];
    
    if (command == "PING") {
        HandlePing();
    } else if (command == "LIST_SYS") {
        HandleListSys();
    } else if (command == "LIST_TEXT") {
        HandleListText();
    } else if (command == "GET_SYS" && parts.size() >= 2) {
        HandleGetSys(parts[1]);
    } else if (command == "GET_TEXT" && parts.size() >= 2) {
        HandleGetText(parts[1]);
    } else if (command == "STATUS") {
        HandleStatus();
    } else if (command == "ADD" && parts.size() >= 3) {
        std::string name = parts[1];
        std::string text = UnescapeString(parts[2]);
        HandleAdd(name, text);
    } else if (command == "MOD" && parts.size() >= 3) {
        std::string name = parts[1];
        std::string text = UnescapeString(parts[2]);
        HandleMod(name, text);
    } else if (command == "DEL" && parts.size() >= 2) {
        HandleDel(parts[1]);
    } else if (command == "EXEC" && parts.size() >= 2) {
        HandleExec(parts[1]);
    } else if (command == "BACKUP") {
        HandleBackup();
    } else if (command == "RESTORE" && parts.size() >= 2) {
        HandleRestore(parts[1]);
    } else if (command == "RESET") {
        HandleReset();
    } else {
        SendErrorResponse("command not found");
    }
}

void UartCommandManager::HandlePing() {
    ESP_LOGI(TAG, "📡 PING received from PC client");
    SendResponse("PONG");
    ESP_LOGI(TAG, "📡 PONG sent to PC client");
}

void UartCommandManager::HandleListSys() {
    ESP_LOGI(TAG, "📋 PC client requesting system commands list");
    std::vector<std::string> sys_commands = GetSystemCommands();
    std::string response;

    for (size_t i = 0; i < sys_commands.size(); ++i) {
        if (i > 0) response += ",";
        response += sys_commands[i];
    }

    ESP_LOGI(TAG, "📋 Sending %d system commands to PC client", sys_commands.size());
    SendOkResponse(response);
}

void UartCommandManager::HandleListText() {
    ESP_LOGI(TAG, "📝 PC client requesting text commands list");
    auto commands = storage_->GetAllCommands();
    std::string response;

    bool first = true;
    for (const auto& pair : commands) {
        if (!first) response += ",";
        response += pair.first + "=" + EscapeString(pair.second);
        first = false;
    }

    ESP_LOGI(TAG, "📝 Sending %d text commands to PC client", commands.size());
    SendOkResponse(response);
}

void UartCommandManager::HandleGetSys(const std::string& cmd_name) {
    std::string description = GetSystemCommandDescription(cmd_name);
    if (description.empty()) {
        SendErrorResponse("system command not found");
    } else {
        SendOkResponse(cmd_name + "=" + EscapeString(description));
    }
}

void UartCommandManager::HandleGetText(const std::string& cmd_name) {
    std::string text = storage_->GetCommand(cmd_name);
    if (text.empty()) {
        SendErrorResponse("text command not found");
    } else {
        SendOkResponse(cmd_name + "=" + EscapeString(text));
    }
}

void UartCommandManager::HandleStatus() {
    ESP_LOGI(TAG, "📊 PC client requesting system status");
    auto commands = storage_->GetAllCommands();
    std::vector<std::string> sys_commands = GetSystemCommands();

    std::ostringstream oss;
    oss << "connected,sys_cmds:" << sys_commands.size()
        << ",text_cmds:" << commands.size()
        << ",storage:ok"
        << ",free_mem:" << SystemInfo::GetFreeHeapSize();

    ESP_LOGI(TAG, "📊 Status: %d system commands, %d text commands, %d KB free memory",
             sys_commands.size(), commands.size(), SystemInfo::GetFreeHeapSize() / 1024);
    SendOkResponse(oss.str());
}

void UartCommandManager::HandleAdd(const std::string& name, const std::string& text) {
    ESP_LOGI(TAG, "➕ PC client adding text command: '%s' = '%s'", name.c_str(), text.c_str());
    esp_err_t err = storage_->AddCommand(name, text);
    if (err == ESP_OK) {
        ESP_LOGI(TAG, "✅ Text command '%s' added successfully", name.c_str());
        SendOkResponse("added");
    } else if (err == ESP_ERR_INVALID_ARG) {
        if (storage_->CommandExists(name)) {
            ESP_LOGW(TAG, "⚠️ Command '%s' already exists", name.c_str());
            SendErrorResponse("command exists");
        } else {
            ESP_LOGE(TAG, "❌ Invalid parameters for command '%s'", name.c_str());
            SendErrorResponse("invalid parameters");
        }
    } else if (err == ESP_ERR_NO_MEM) {
        ESP_LOGE(TAG, "❌ Storage full, cannot add command '%s'", name.c_str());
        SendErrorResponse("storage full");
    } else {
        ESP_LOGE(TAG, "❌ Storage failed for command '%s': %s", name.c_str(), esp_err_to_name(err));
        SendErrorResponse("storage failed");
    }
}

void UartCommandManager::HandleMod(const std::string& name, const std::string& text) {
    esp_err_t err = storage_->ModifyCommand(name, text);
    if (err == ESP_OK) {
        SendOkResponse("modified");
    } else if (err == ESP_ERR_NOT_FOUND) {
        SendErrorResponse("command not found");
    } else if (err == ESP_ERR_INVALID_ARG) {
        SendErrorResponse("invalid parameters");
    } else {
        SendErrorResponse("storage failed");
    }
}

void UartCommandManager::HandleDel(const std::string& name) {
    esp_err_t err = storage_->DeleteCommand(name);
    if (err == ESP_OK) {
        SendOkResponse("deleted");
    } else if (err == ESP_ERR_NOT_FOUND) {
        SendErrorResponse("command not found");
    } else {
        SendErrorResponse("storage failed");
    }
}

void UartCommandManager::HandleExec(const std::string& name) {
    ESP_LOGI(TAG, "🚀 PC client executing command: '%s'", name.c_str());

    // 首先检查是否是存储的文本指令
    std::string text = storage_->GetCommand(name);
    if (!text.empty()) {
        ESP_LOGI(TAG, "🚀 Executing text command '%s': '%s'", name.c_str(), text.c_str());
        // 执行文本指令
        bool success = text_interaction::TextCommands::ExecuteTextCommand(text);
        if (success) {
            ESP_LOGI(TAG, "✅ Text command '%s' executed successfully", name.c_str());
            SendOkResponse("executed");
        } else {
            ESP_LOGE(TAG, "❌ Text command '%s' execution failed", name.c_str());
            SendErrorResponse("execution failed");
        }
        return;
    }

    // 检查是否是系统指令
    std::vector<std::string> sys_commands = GetSystemCommands();
    if (std::find(sys_commands.begin(), sys_commands.end(), name) != sys_commands.end()) {
        ESP_LOGW(TAG, "⚠️ System command '%s' is protected", name.c_str());
        SendErrorResponse("system command protected");
        return;
    }

    ESP_LOGW(TAG, "⚠️ Command '%s' not found", name.c_str());
    SendErrorResponse("command not found");
}

void UartCommandManager::HandleBackup() {
    auto commands = storage_->GetAllCommands();
    std::string response;
    
    bool first = true;
    for (const auto& pair : commands) {
        if (!first) response += ",";
        response += pair.first + "=" + EscapeString(pair.second);
        first = false;
    }
    
    SendOkResponse(response);
}

void UartCommandManager::HandleRestore(const std::string& data) {
    std::map<std::string, std::string> commands;
    
    // 解析恢复数据：name1=text1,name2=text2,...
    std::vector<std::string> pairs = SplitString(data, ',');
    for (const std::string& pair : pairs) {
        size_t eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            std::string name = pair.substr(0, eq_pos);
            std::string text = UnescapeString(pair.substr(eq_pos + 1));
            commands[name] = text;
        }
    }
    
    if (commands.empty()) {
        SendErrorResponse("invalid restore data");
        return;
    }
    
    esp_err_t err = storage_->ImportCommands(commands);
    if (err == ESP_OK) {
        SendOkResponse("restored");
    } else {
        SendErrorResponse("restore failed");
    }
}

void UartCommandManager::HandleReset() {
    esp_err_t err = storage_->ClearAllCommands();
    if (err == ESP_OK) {
        SendOkResponse("reset");
    } else {
        SendErrorResponse("reset failed");
    }
}

void UartCommandManager::SendOkResponse(const std::string& data) {
    std::string response = "OK";
    if (!data.empty()) {
        response += ":" + data;
    }
    SendResponse(response);
}

void UartCommandManager::SendErrorResponse(const std::string& error) {
    SendResponse("ERROR:" + error);
}

void UartCommandManager::SendResponse(const std::string& response) {
    if (response_callback_) {
        response_callback_(response + "\n");
        ESP_LOGI(TAG, "📤 Sent to PC client: '%s'", response.c_str());
    } else {
        ESP_LOGW(TAG, "⚠️ No response callback set, response not sent: '%s'", response.c_str());
    }
}

std::vector<std::string> UartCommandManager::SplitString(const std::string& str, char delimiter) {
    std::vector<std::string> result;
    std::stringstream ss(str);
    std::string item;
    
    while (std::getline(ss, item, delimiter)) {
        result.push_back(item);
    }
    
    return result;
}

std::vector<std::string> UartCommandManager::GetSystemCommands() {
    // 返回真正的系统管理指令列表
    return {
        "help", "wifi", "4g", "reboot", "ota", "settings"
    };
}

std::string UartCommandManager::GetSystemCommandDescription(const std::string& cmd_name) {
    // 系统指令描述映射
    static const std::map<std::string, std::string> descriptions = {
        {"help", "显示所有可用命令的帮助信息"},
        {"wifi", "WiFi网络连接和管理"},
        {"4g", "4G网络连接和管理"},
        {"reboot", "重启设备"},
        {"ota", "固件在线更新"},
        {"settings", "系统设置管理"}
    };

    auto it = descriptions.find(cmd_name);
    return (it != descriptions.end()) ? it->second : "";
}

std::string UartCommandManager::EscapeString(const std::string& str) {
    std::string result;
    for (char c : str) {
        switch (c) {
            case '|': result += "\\p"; break;
            case '\\': result += "\\\\"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            default: result += c; break;
        }
    }
    return result;
}

std::string UartCommandManager::UnescapeString(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '\\' && i + 1 < str.length()) {
            switch (str[i + 1]) {
                case 'p': result += '|'; i++; break;
                case '\\': result += '\\'; i++; break;
                case 'n': result += '\n'; i++; break;
                case 'r': result += '\r'; i++; break;
                default: result += str[i]; break;
            }
        } else {
            result += str[i];
        }
    }
    return result;
}

} // namespace communication
