#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智TTS音频批量接收工具
从ESP32串口接收批量导出的TTS音频文件并保存到电脑
"""

import serial
import os
import re
import time
from pathlib import Path

class TTSBatchReceiver:
    """TTS音频批量接收器"""
    
    def __init__(self, port='COM13', baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.output_dir = Path("J:/xiaozhi-esp32/audio_files")
        self.output_dir.mkdir(exist_ok=True)
        
    def connect_to_device(self):
        """连接到ESP32设备"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=2)
            print(f"✅ 已连接到设备: {self.port}")
            time.sleep(1)  # 等待连接稳定
            return True
        except Exception as e:
            print(f"❌ 连接设备失败: {e}")
            return False
    
    def send_export_command(self):
        """发送批量导出命令"""
        try:
            print("📤 发送批量导出命令: tts_export_all")
            self.ser.write(b"tts_export_all\n")
            time.sleep(1)  # 等待命令执行
            return True
        except Exception as e:
            print(f"❌ 发送命令失败: {e}")
            return False
    
    def receive_batch_export(self, timeout=60):
        """接收批量导出数据"""
        print("📥 开始接收批量导出数据...")
        
        response = ""
        start_time = time.time()
        in_export_section = False
        files_data = {}
        current_file = None
        current_size = 0
        current_hex_data = ""
        
        while time.time() - start_time < timeout:
            if self.ser.in_waiting > 0:
                data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                response += data
                
                # 逐行处理数据
                lines = response.split('\n')
                response = lines[-1]  # 保留最后一行（可能不完整）
                
                for line in lines[:-1]:  # 处理完整的行
                    line = line.strip()
                    
                    # 检测批量导出开始
                    if "=== BATCH_EXPORT_START ===" in line:
                        in_export_section = True
                        print("🚀 检测到批量导出开始")
                        continue
                    
                    # 检测批量导出结束
                    if "=== BATCH_EXPORT_END ===" in line:
                        in_export_section = False
                        print("🎉 批量导出数据接收完成")
                        break
                    
                    if not in_export_section:
                        continue
                    
                    # 检测文件开始
                    if line.startswith("--- FILE_START:"):
                        match = re.search(r'--- FILE_START: (.+) ---', line)
                        if match:
                            current_file = match.group(1)
                            current_hex_data = ""
                            print(f"📄 开始接收文件: {current_file}")
                        continue
                    
                    # 检测文件大小
                    if line.startswith("SIZE:"):
                        match = re.search(r'SIZE: (\d+)', line)
                        if match:
                            current_size = int(match.group(1))
                            print(f"   文件大小: {current_size} 字节")
                        continue
                    
                    # 检测十六进制数据开始
                    if line == "HEX_DATA:":
                        continue
                    
                    # 检测文件结束
                    if line.startswith("--- FILE_END:"):
                        if current_file and current_hex_data:
                            files_data[current_file] = {
                                'size': current_size,
                                'hex_data': current_hex_data
                            }
                            print(f"✅ 文件 {current_file} 数据接收完成")
                        current_file = None
                        current_hex_data = ""
                        continue
                    
                    # 收集十六进制数据
                    if current_file and re.match(r'^[0-9A-F]+$', line):
                        current_hex_data += line
            
            time.sleep(0.1)
        
        return files_data
    
    def save_files(self, files_data):
        """保存文件到电脑"""
        if not files_data:
            print("❌ 没有接收到文件数据")
            return False
        
        print(f"\n💾 开始保存 {len(files_data)} 个文件...")
        
        success_count = 0
        for filename, file_info in files_data.items():
            try:
                # 解析十六进制数据
                hex_data = file_info['hex_data']
                expected_size = file_info['size']
                
                # 转换为字节数据
                byte_data = bytes.fromhex(hex_data)
                
                # 验证大小
                if len(byte_data) != expected_size:
                    print(f"⚠️  {filename}: 大小不匹配 (期望: {expected_size}, 实际: {len(byte_data)})")
                
                # 保存文件
                output_file = self.output_dir / filename
                with open(output_file, 'wb') as f:
                    f.write(byte_data)
                
                print(f"✅ {filename}: 已保存 ({len(byte_data)} 字节)")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {filename}: 保存失败 - {e}")
        
        print(f"\n🎉 保存完成! 成功: {success_count}/{len(files_data)}")
        print(f"📂 文件保存位置: {self.output_dir}")
        
        return success_count > 0
    
    def list_saved_files(self):
        """列出已保存的文件"""
        print(f"\n📋 已保存的TTS文件:")
        p3_files = list(self.output_dir.glob("tts_*.p3"))
        
        if not p3_files:
            print("   (无文件)")
            return
        
        total_size = 0
        for p3_file in sorted(p3_files):
            size = p3_file.stat().st_size
            total_size += size
            print(f"   {p3_file.name}: {size} 字节")
        
        print(f"   总计: {len(p3_files)} 个文件, {total_size} 字节 ({total_size/1024:.1f} KB)")
    
    def run_batch_export(self):
        """运行批量导出流程"""
        if not self.connect_to_device():
            return False
        
        try:
            # 发送导出命令
            if not self.send_export_command():
                return False
            
            # 接收数据
            files_data = self.receive_batch_export()
            
            # 保存文件
            if self.save_files(files_data):
                self.list_saved_files()
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ 批量导出过程出错: {e}")
            return False
        
        finally:
            if hasattr(self, 'ser'):
                self.ser.close()
                print("🔌 设备连接已关闭")

def main():
    """主函数"""
    print("🎵 小智TTS音频批量接收工具")
    print("=" * 50)
    
    print("💡 使用说明:")
    print("1. 确保ESP32已连接并运行小智程序")
    print("2. 本工具将自动发送 'tts_export_all' 命令")
    print("3. 接收所有TTS文件并保存到电脑")
    print("4. 文件将保存到: J:/xiaozhi-esp32/audio_files/")
    print()
    
    # 创建接收器
    receiver = TTSBatchReceiver()
    
    # 运行批量导出
    if receiver.run_batch_export():
        print("\n✅ 批量导出成功完成!")
    else:
        print("\n❌ 批量导出失败")

if __name__ == "__main__":
    main()
