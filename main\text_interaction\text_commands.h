#pragma once

#include <string>
#include <map>

namespace text_interaction {

// 前向声明
class TextInteractionManager;

/**
 * @brief 自定义指令信息结构
 */
struct CustomCommand {
    std::string text;         ///< 指令对应的文本内容
    std::string description;  ///< 指令描述

    CustomCommand() = default;
    CustomCommand(const std::string& t, const std::string& d = "")
        : text(t), description(d) {}
};

/**
 * @brief 文本交互命令处理器
 * 
 * 提供各种文本交互命令的实现，包括：
 * - 基础ask命令
 * - 快捷命令（hello, weather, time, joke等）
 * - 命令注册和管理
 */
class TextCommands {
public:
    /**
     * @brief 初始化文本交互命令系统
     * @param manager 文本交互管理器实例
     * @return true 初始化成功，false 初始化失败
     */
    static bool Initialize(TextInteractionManager* manager);

    /**
     * @brief 注册所有文本交互命令到ESP控制台
     */
    static void RegisterCommands();

    /**
     * @brief 获取文本交互管理器实例
     * @return 管理器实例指针，如果未初始化则返回nullptr
     */
    static TextInteractionManager* GetManager();

    /**
     * @brief 执行文本指令（用于UART指令管理器）
     * @param text 要发送给小智的文本内容
     * @return true 执行成功，false 执行失败
     */
    static bool ExecuteTextCommand(const std::string& text);

    // ========== 指令配置管理功能 ==========

    /**
     * @brief 添加自定义指令
     * @param command 指令名称
     * @param text 对应的文本内容
     * @param description 指令描述（可选）
     * @return true 添加成功，false 添加失败
     */
    static bool AddCustomCommand(const std::string& command, const std::string& text, const std::string& description = "");

    /**
     * @brief 删除自定义指令
     * @param command 指令名称
     * @return true 删除成功，false 删除失败
     */
    static bool RemoveCustomCommand(const std::string& command);

    /**
     * @brief 修改自定义指令的文本内容
     * @param command 指令名称
     * @param new_text 新的文本内容
     * @return true 修改成功，false 修改失败
     */
    static bool ModifyCustomCommand(const std::string& command, const std::string& new_text);

    /**
     * @brief 列出所有自定义指令
     */
    static void ListCustomCommands();

    /**
     * @brief 保存自定义指令配置到存储器
     * @return true 保存成功，false 保存失败
     */
    static bool SaveCustomCommands();

    /**
     * @brief 从存储器加载自定义指令配置
     * @return true 加载成功，false 加载失败
     */
    static bool LoadCustomCommands();

    // ========== 命令处理函数 ==========

    /**
     * @brief ask命令处理函数
     * 用法: ask <message>
     * 示例: ask 今天天气怎么样
     */
    static int CmdAsk(int argc, char** argv);

    /**
     * @brief say命令处理函数（简化版ask）
     * 用法: say <message>
     * 示例: say hello, say weather
     */
    static int CmdSay(int argc, char** argv);

    /**
     * @brief now命令处理函数
     * 用法: now
     * 功能: 快速获取当前时间和天气信息
     */
    static int CmdNow(int argc, char** argv);

    /**
     * @brief hello命令处理函数
     * 用法: hello
     * 快速向小智问好
     */
    static int CmdHello(int argc, char** argv);

    /**
     * @brief weather命令处理函数
     * 用法: weather [city]
     * 示例: weather 北京
     */
    static int CmdWeather(int argc, char** argv);

    /**
     * @brief time命令处理函数
     * 用法: time
     * 询问当前时间
     */
    static int CmdTime(int argc, char** argv);

    /**
     * @brief joke命令处理函数
     * 用法: joke
     * 请小智讲个笑话
     */
    static int CmdJoke(int argc, char** argv);

    /**
     * @brief 文本交互帮助命令
     * 用法: texthelp
     * 显示所有文本交互命令的帮助信息
     */
    static int CmdTextHelp(int argc, char** argv);

    /**
     * @brief 文本交互状态命令
     * 用法: textstatus
     * 显示文本交互系统的当前状态
     */
    static int CmdTextStatus(int argc, char** argv);

    // ========== 指令配置管理命令 ==========

    /**
     * @brief cmdadd命令处理函数
     * 用法: cmdadd <command> <text> [description]
     * 示例: cmdadd music "播放一首好听的音乐" "音乐播放指令"
     */
    static int CmdAdd(int argc, char** argv);

    /**
     * @brief cmdlist命令处理函数
     * 用法: cmdlist
     * 显示所有自定义指令
     */
    static int CmdList(int argc, char** argv);

    /**
     * @brief cmdmodify命令处理函数
     * 用法: cmdmodify <command> <new_text>
     * 示例: cmdmodify hello "你好小智，很高兴见到你"
     */
    static int CmdModify(int argc, char** argv);

    /**
     * @brief cmdremove命令处理函数
     * 用法: cmdremove <command>
     * 示例: cmdremove music
     */
    static int CmdRemove(int argc, char** argv);

    /**
     * @brief cmdsave命令处理函数
     * 用法: cmdsave
     * 保存所有自定义指令到存储器
     */
    static int CmdSave(int argc, char** argv);

    /**
     * @brief cmdload命令处理函数
     * 用法: cmdload
     * 从存储器加载自定义指令
     */
    static int CmdLoad(int argc, char** argv);

    /**
     * @brief custom命令处理函数
     * 用法: custom <command_name>
     * 执行指定的自定义指令
     */
    static int CmdCustom(int argc, char** argv);

    /**
     * @brief raw命令处理函数（调试用）
     * 用法: raw <message>
     * 功能: 直接发送原始消息，不进行编码转换
     */
    static int CmdRaw(int argc, char** argv);

private:
    /**
     * @brief 验证参数并构建消息
     * @param argc 参数数量
     * @param argv 参数数组
     * @param start_index 开始拼接的参数索引
     * @param message 输出的消息字符串
     * @return true 参数有效，false 参数无效
     */
    static bool BuildMessageFromArgs(int argc, char** argv, int start_index, std::string& message);

    /**
     * @brief 打印命令使用说明
     * @param command 命令名称
     * @param usage 使用方法
     * @param example 示例（可选）
     */
    static void PrintUsage(const char* command, const char* usage, const char* example = nullptr);

    /**
     * @brief 发送消息并显示状态
     * @param message 要发送的消息
     * @param action_description 动作描述（如"查询天气"）
     * @return true 发送成功，false 发送失败
     */
    static bool SendMessageWithStatus(const std::string& message, const char* action_description);

private:
    static TextInteractionManager* manager_;  ///< 文本交互管理器实例
    static bool initialized_;                 ///< 初始化状态标志

    // 自定义指令存储
    static std::map<std::string, CustomCommand> custom_commands_;  ///< 自定义指令映射表
    static const char* CUSTOM_COMMANDS_NVS_KEY;                   ///< NVS存储键名

    /**
     * @brief 动态注册自定义指令到控制台（简化实现）
     * @param command 指令名称
     * @param custom_cmd 自定义指令信息
     * @return true 注册成功，false 注册失败
     */
    static bool RegisterCustomCommand(const std::string& command, const CustomCommand& custom_cmd);

    /**
     * @brief 动态注销自定义指令（简化实现）
     * @param command 指令名称
     * @return true 注销成功，false 注销失败
     */
    static bool UnregisterCustomCommand(const std::string& command);

    /**
     * @brief 注册所有已加载的自定义指令到控制台
     */
    static void RegisterCustomCommands();

#ifdef CONFIG_ENABLE_CAR_VOICE_TRIGGER
    // ========== 汽车语音触发调试命令 ==========

    /**
     * @brief 汽车语音触发状态命令
     * 用法: carstatus
     * 显示汽车语音触发系统的当前状态
     */
    static int CmdCarStatus(int argc, char** argv);

    /**
     * @brief 手动播放汽车语音命令
     * 用法: carplay <voice_id>
     * 示例: carplay 2
     */
    static int CmdCarPlay(int argc, char** argv);

    /**
     * @brief 注入测试汽车数据命令
     * 用法: carinject <hex_data>
     * 示例: carinject AA010800010203040506
     */
    static int CmdCarInject(int argc, char** argv);

    /**
     * @brief 启用/禁用汽车语音触发命令
     * 用法: carenable <0|1>
     * 示例: carenable 1
     */
    static int CmdCarEnable(int argc, char** argv);
#endif


};

} // namespace text_interaction
