#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

// 如果使用 Duplex I2S 模式，请注释下面一行
#define AUDIO_I2S_METHOD_SIMPLEX

#ifdef AUDIO_I2S_METHOD_SIMPLEX

#define AUDIO_I2S_MIC_GPIO_WS   GPIO_NUM_4
#define AUDIO_I2S_MIC_GPIO_SCK  GPIO_NUM_5
#define AUDIO_I2S_MIC_GPIO_DIN  GPIO_NUM_6
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16

#else

#define AUDIO_I2S_GPIO_WS GPIO_NUM_4
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_5
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_6
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_7

#endif

#define BUILTIN_LED_GPIO        GPIO_NUM_48
#define BOOT_BUTTON_GPIO        GPIO_NUM_0
#define TOUCH_BUTTON_GPIO       GPIO_NUM_47
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_40
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_39

// Display disabled - no display hardware connected
// #define DISPLAY_SDA_PIN GPIO_NUM_41
// #define DISPLAY_SCL_PIN GPIO_NUM_42
// #define DISPLAY_WIDTH   128
// #define DISPLAY_HEIGHT  32
// #define DISPLAY_MIRROR_X true
// #define DISPLAY_MIRROR_Y true


#define ML307_RX_PIN GPIO_NUM_11
#define ML307_TX_PIN GPIO_NUM_12
#define ML307_POWER_PIN GPIO_NUM_13  // ML307模块电源控制引脚 (EN引脚)


// A MCP Test: Control a lamp
#define LAMP_GPIO GPIO_NUM_18

// 🔊 临时PWM音频输出 (用于测试音频数据流)
#define PWM_AUDIO_GPIO GPIO_NUM_8  // 使用GPIO8作为PWM音频输出

#endif // _BOARD_CONFIG_H_
