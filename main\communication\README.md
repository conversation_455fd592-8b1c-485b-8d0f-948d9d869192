# 通讯功能模块

## 概述

本模块提供ESP32与电脑端的串口通信功能，实现对小智设备文本指令的远程管理。

## 模块结构

```
communication/
├── uart_command_manager.h/cc    # UART指令管理器
├── text_command_storage.h/cc    # 文本指令存储器
└── README.md                    # 本文档
```

## 功能特性

### 1. 文本指令存储 (TextCommandStorage)
- **NVS持久化存储**：自定义指令保存在NVS中，重启后不丢失
- **增删改查操作**：支持完整的CRUD操作
- **数据验证**：指令名称和内容的有效性检查
- **批量操作**：支持批量导入导出指令
- **存储限制**：最多50个指令，每个指令名称最长32字符，内容最长500字符

### 2. UART指令管理 (UartCommandManager)
- **简化文本协议**：使用`CMD:PARAM1:PARAM2\n`格式
- **双向通信**：支持查询和管理操作
- **错误处理**：完善的错误响应机制
- **字符转义**：处理特殊字符的转义和反转义

## 支持的指令

### 连接测试
- `PING` → `PONG`

### 查询类指令
- `LIST_SYS` - 获取系统指令列表
- `LIST_TEXT` - 获取文本指令列表
- `GET_SYS:name` - 获取系统指令详情
- `GET_TEXT:name` - 获取文本指令详情
- `STATUS` - 获取系统状态

### 管理类指令
- `ADD:name:text` - 添加文本指令
- `MOD:name:newtext` - 修改文本指令
- `DEL:name` - 删除文本指令
- `EXEC:name` - 执行指令测试

### 批量操作
- `BACKUP` - 备份所有自定义指令
- `RESTORE:data` - 恢复指令
- `RESET` - 重置所有自定义指令

## 使用方法

### 1. 初始化
```cpp
#include "communication/uart_command_manager.h"

UartCommandManager* uart_cmd_manager = new UartCommandManager();
uart_cmd_manager->Initialize(text_commands, network_console);

// 设置响应回调
uart_cmd_manager->SetResponseCallback([](const std::string& response) {
    // 发送响应到UART2
    uart_write_bytes(UART_NUM_2, response.c_str(), response.length());
});
```

### 2. 处理接收数据
```cpp
// 在UART接收回调中
void uart_receive_callback(const char* data, size_t len) {
    std::string received_data(data, len);
    uart_cmd_manager->ProcessReceivedData(received_data);
}
```

### 3. 集成到现有系统
```cpp
// 在network_console.cc中添加UART指令处理
void NetworkConsole::ProcessUartData(const std::string& data) {
    if (uart_cmd_manager_) {
        uart_cmd_manager_->ProcessReceivedData(data);
    }
}
```

## 协议格式

### 请求格式
```
COMMAND:PARAM1:PARAM2\n
```

### 响应格式
```
OK:data\n          # 成功响应
ERROR:message\n    # 错误响应
PONG\n            # PING响应
```

### 字符转义
- `|` → `\p`
- `\` → `\\`
- `\n` → `\n`
- `\r` → `\r`

## 错误码

| 错误信息 | 说明 |
|----------|------|
| `command not found` | 指令不存在 |
| `invalid parameters` | 参数错误 |
| `storage failed` | 存储操作失败 |
| `command exists` | 指令已存在 |
| `storage full` | 存储空间已满 |
| `execution failed` | 指令执行失败 |

## 配置参数

### TextCommandStorage
- `MAX_COMMAND_COUNT`: 50 (最大指令数量)
- `MAX_COMMAND_NAME_LENGTH`: 32 (指令名称最大长度)
- `MAX_COMMAND_TEXT_LENGTH`: 500 (指令内容最大长度)
- `NVS_NAMESPACE`: "uart_cmds" (NVS命名空间)

### UartCommandManager
- 支持的系统指令：help, wifi, 4g, reboot, ask, now, time, weather, car_gpio, cmd_gpio, gpio_status, ota, settings

## 注意事项

1. **线程安全**：模块不是线程安全的，需要在同一线程中使用
2. **内存管理**：使用完毕后需要正确释放资源
3. **NVS初始化**：确保NVS已正确初始化
4. **UART配置**：确保UART2已切换到指令模式
5. **字符编码**：使用UTF-8编码处理中文内容

## 扩展功能

### 未来可扩展的功能
- 指令执行统计
- 指令分组管理
- 权限控制
- 指令模板
- 远程配置同步

## 调试信息

模块使用ESP_LOG输出调试信息，标签为：
- `TextCommandStorage` - 存储相关日志
- `UartCommandManager` - 指令管理相关日志

可通过menuconfig调整日志级别进行调试。
