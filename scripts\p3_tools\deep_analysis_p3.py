#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度全面分析P3文件 - 对比我们转换的文件与999.p3的完全一致性
"""

import struct
import os
import sys
import numpy as np
import hashlib

def deep_comprehensive_analysis(file1, file2):
    """
    深度全面分析两个P3文件的一致性
    """
    print("🔍 深度全面P3文件分析")
    print("=" * 80)
    print(f"文件1 (小智原版): {file1}")
    print(f"文件2 (我们转换): {file2}")
    print("=" * 80)
    
    # 1. 文件级别分析
    file_analysis = analyze_file_level(file1, file2)
    
    # 2. 数据包级别分析
    packets1 = read_p3_packets(file1)
    packets2 = read_p3_packets(file2)
    packet_analysis = analyze_packet_level(packets1, packets2)
    
    # 3. Opus编码分析
    opus_analysis = analyze_opus_encoding(packets1, packets2)
    
    # 4. 二进制级别分析
    binary_analysis = analyze_binary_level(packets1, packets2)
    
    # 5. 统计特征分析
    stats_analysis = analyze_statistical_features(packets1, packets2)
    
    # 6. 生成综合报告
    generate_comprehensive_report(file_analysis, packet_analysis, opus_analysis, 
                                binary_analysis, stats_analysis)

def analyze_file_level(file1, file2):
    """
    文件级别分析
    """
    print("\n📁 1. 文件级别分析")
    print("-" * 50)
    
    analysis = {}
    
    # 文件大小
    size1 = os.path.getsize(file1)
    size2 = os.path.getsize(file2)
    analysis['file_sizes'] = (size1, size2)
    print(f"文件大小: {size1} vs {size2} 字节")
    
    # 文件哈希
    with open(file1, 'rb') as f:
        hash1 = hashlib.md5(f.read()).hexdigest()
    with open(file2, 'rb') as f:
        hash2 = hashlib.md5(f.read()).hexdigest()
    
    analysis['file_hashes'] = (hash1, hash2)
    print(f"MD5哈希: {hash1[:16]}... vs {hash2[:16]}...")
    print(f"文件完全相同: {'✅ 是' if hash1 == hash2 else '❌ 否'}")
    
    return analysis

def analyze_packet_level(packets1, packets2):
    """
    数据包级别分析
    """
    print("\n📦 2. 数据包级别分析")
    print("-" * 50)
    
    analysis = {}
    
    # 数据包数量
    count1, count2 = len(packets1), len(packets2)
    analysis['packet_counts'] = (count1, count2)
    print(f"数据包数量: {count1} vs {count2}")
    
    # 数据包大小分布
    sizes1 = [len(p) for p in packets1]
    sizes2 = [len(p) for p in packets2]
    
    analysis['size_stats'] = {
        'file1': {'min': min(sizes1), 'max': max(sizes1), 'avg': np.mean(sizes1), 'std': np.std(sizes1)},
        'file2': {'min': min(sizes2), 'max': max(sizes2), 'avg': np.mean(sizes2), 'std': np.std(sizes2)}
    }
    
    print(f"数据包大小统计:")
    print(f"  文件1: 最小={min(sizes1)}, 最大={max(sizes1)}, 平均={np.mean(sizes1):.1f}, 标准差={np.std(sizes1):.1f}")
    print(f"  文件2: 最小={min(sizes2)}, 最大={max(sizes2)}, 平均={np.mean(sizes2):.1f}, 标准差={np.std(sizes2):.1f}")
    
    # 大小分布相似性
    if count1 == count2:
        size_diff = [abs(s1 - s2) for s1, s2 in zip(sizes1, sizes2)]
        analysis['size_differences'] = size_diff
        print(f"  大小差异: 平均={np.mean(size_diff):.1f}, 最大={max(size_diff)}")
    
    return analysis

def analyze_opus_encoding(packets1, packets2):
    """
    Opus编码分析
    """
    print("\n🎵 3. Opus编码深度分析")
    print("-" * 50)
    
    analysis = {}
    
    # 分析前10个数据包的Opus特征
    for i in range(min(10, len(packets1), len(packets2))):
        print(f"\n数据包 {i+1}:")
        
        p1, p2 = packets1[i], packets2[i]
        
        # TOC字节分析
        if len(p1) > 0 and len(p2) > 0:
            toc1, toc2 = p1[0], p2[0]
            
            # 解析TOC字节
            config1 = (toc1 >> 3) & 0x1F
            config2 = (toc2 >> 3) & 0x1F
            
            stereo1 = (toc1 >> 2) & 0x01
            stereo2 = (toc2 >> 2) & 0x01
            
            frames1 = toc1 & 0x03
            frames2 = toc2 & 0x03
            
            print(f"  TOC字节: 0x{toc1:02X} vs 0x{toc2:02X} {'✅' if toc1 == toc2 else '❌'}")
            print(f"  配置: {config1} vs {config2} {'✅' if config1 == config2 else '❌'}")
            print(f"  立体声: {stereo1} vs {stereo2} {'✅' if stereo1 == stereo2 else '❌'}")
            print(f"  帧数: {frames1} vs {frames2} {'✅' if frames1 == frames2 else '❌'}")
            
            # 数据内容相似性
            if len(p1) == len(p2):
                identical_bytes = sum(1 for b1, b2 in zip(p1, p2) if b1 == b2)
                similarity = identical_bytes / len(p1) * 100
                print(f"  内容相似度: {similarity:.1f}%")
            else:
                print(f"  大小不同: {len(p1)} vs {len(p2)} 字节")
    
    # 整体TOC字节统计
    toc_bytes1 = [p[0] for p in packets1 if len(p) > 0]
    toc_bytes2 = [p[0] for p in packets2 if len(p) > 0]
    
    analysis['toc_consistency'] = {
        'file1_unique_tocs': len(set(toc_bytes1)),
        'file2_unique_tocs': len(set(toc_bytes2)),
        'file1_dominant_toc': max(set(toc_bytes1), key=toc_bytes1.count) if toc_bytes1 else None,
        'file2_dominant_toc': max(set(toc_bytes2), key=toc_bytes2.count) if toc_bytes2 else None
    }
    
    print(f"\nTOC字节一致性:")
    print(f"  文件1唯一TOC数: {analysis['toc_consistency']['file1_unique_tocs']}")
    print(f"  文件2唯一TOC数: {analysis['toc_consistency']['file2_unique_tocs']}")
    print(f"  文件1主要TOC: 0x{analysis['toc_consistency']['file1_dominant_toc']:02X}")
    print(f"  文件2主要TOC: 0x{analysis['toc_consistency']['file2_dominant_toc']:02X}")
    
    return analysis

def analyze_binary_level(packets1, packets2):
    """
    二进制级别分析
    """
    print("\n🔢 4. 二进制级别深度分析")
    print("-" * 50)
    
    analysis = {}
    
    # 字节频率分析
    all_bytes1 = b''.join(packets1)
    all_bytes2 = b''.join(packets2)
    
    freq1 = np.bincount(list(all_bytes1), minlength=256)
    freq2 = np.bincount(list(all_bytes2), minlength=256)
    
    # 计算字节分布相似性
    freq1_norm = freq1 / np.sum(freq1)
    freq2_norm = freq2 / np.sum(freq2)
    
    # KL散度
    kl_div = np.sum(freq1_norm * np.log((freq1_norm + 1e-10) / (freq2_norm + 1e-10)))
    analysis['kl_divergence'] = kl_div
    
    print(f"字节分布KL散度: {kl_div:.6f} (越小越相似)")
    
    # 熵分析
    entropy1 = -np.sum(freq1_norm * np.log2(freq1_norm + 1e-10))
    entropy2 = -np.sum(freq2_norm * np.log2(freq2_norm + 1e-10))
    
    analysis['entropy'] = (entropy1, entropy2)
    print(f"信息熵: {entropy1:.3f} vs {entropy2:.3f}")
    
    # 最常见字节
    top_bytes1 = sorted(enumerate(freq1), key=lambda x: x[1], reverse=True)[:10]
    top_bytes2 = sorted(enumerate(freq2), key=lambda x: x[1], reverse=True)[:10]
    
    print(f"最常见字节 (前5个):")
    for i in range(5):
        b1, f1 = top_bytes1[i]
        b2, f2 = top_bytes2[i]
        print(f"  #{i+1}: 0x{b1:02X}({f1}) vs 0x{b2:02X}({f2})")
    
    return analysis

def analyze_statistical_features(packets1, packets2):
    """
    统计特征分析
    """
    print("\n📊 5. 统计特征分析")
    print("-" * 50)
    
    analysis = {}
    
    # 数据包间隔分析
    sizes1 = [len(p) for p in packets1]
    sizes2 = [len(p) for p in packets2]
    
    if len(sizes1) > 1 and len(sizes2) > 1:
        diffs1 = np.diff(sizes1)
        diffs2 = np.diff(sizes2)
        
        analysis['size_variation'] = {
            'file1_std': np.std(diffs1),
            'file2_std': np.std(diffs2),
            'file1_range': np.max(diffs1) - np.min(diffs1),
            'file2_range': np.max(diffs2) - np.min(diffs2)
        }
        
        print(f"大小变化模式:")
        print(f"  文件1变化标准差: {np.std(diffs1):.2f}")
        print(f"  文件2变化标准差: {np.std(diffs2):.2f}")
        print(f"  文件1变化范围: {np.max(diffs1) - np.min(diffs1)}")
        print(f"  文件2变化范围: {np.max(diffs2) - np.min(diffs2)}")
    
    # 数据包内容模式分析
    patterns1 = analyze_packet_patterns(packets1)
    patterns2 = analyze_packet_patterns(packets2)
    
    analysis['patterns'] = {'file1': patterns1, 'file2': patterns2}
    
    print(f"数据包模式:")
    print(f"  文件1重复模式: {patterns1['repeated_sequences']}")
    print(f"  文件2重复模式: {patterns2['repeated_sequences']}")
    
    return analysis

def analyze_packet_patterns(packets):
    """
    分析数据包内的模式
    """
    patterns = {
        'repeated_sequences': 0,
        'zero_bytes': 0,
        'max_consecutive_same': 0
    }
    
    for packet in packets:
        if len(packet) < 2:
            continue
            
        # 统计零字节
        patterns['zero_bytes'] += packet.count(0)
        
        # 统计最大连续相同字节
        max_consecutive = 1
        current_consecutive = 1
        
        for i in range(1, len(packet)):
            if packet[i] == packet[i-1]:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 1
        
        patterns['max_consecutive_same'] = max(patterns['max_consecutive_same'], max_consecutive)
    
    return patterns

def generate_comprehensive_report(file_analysis, packet_analysis, opus_analysis, 
                                binary_analysis, stats_analysis):
    """
    生成综合分析报告
    """
    print("\n" + "=" * 80)
    print("📋 综合分析报告")
    print("=" * 80)
    
    # 一致性评分
    consistency_score = 0
    total_checks = 0
    
    # 1. 文件级别一致性
    if file_analysis['file_hashes'][0] == file_analysis['file_hashes'][1]:
        consistency_score += 100
        print("✅ 文件完全相同 (100分)")
    else:
        print("❌ 文件不完全相同")
        
        # 2. Opus配置一致性
        toc1 = opus_analysis['toc_consistency']['file1_dominant_toc']
        toc2 = opus_analysis['toc_consistency']['file2_dominant_toc']
        
        if toc1 == toc2:
            consistency_score += 25
            print("✅ Opus配置一致 (25分)")
        else:
            print(f"❌ Opus配置不一致: 0x{toc1:02X} vs 0x{toc2:02X}")
        
        # 3. 数据包结构一致性
        if packet_analysis['packet_counts'][0] == packet_analysis['packet_counts'][1]:
            consistency_score += 15
            print("✅ 数据包数量一致 (15分)")
        else:
            print(f"❌ 数据包数量不一致: {packet_analysis['packet_counts'][0]} vs {packet_analysis['packet_counts'][1]}")
        
        # 4. 大小分布一致性
        size_stats1 = packet_analysis['size_stats']['file1']
        size_stats2 = packet_analysis['size_stats']['file2']
        
        size_similarity = 0
        if size_stats1['min'] == size_stats2['min']:
            size_similarity += 5
        if size_stats1['max'] == size_stats2['max']:
            size_similarity += 5
        if abs(size_stats1['avg'] - size_stats2['avg']) < 10:
            size_similarity += 10
        
        consistency_score += size_similarity
        print(f"📏 大小分布相似性 ({size_similarity}/20分)")
        
        # 5. 二进制相似性
        kl_div = binary_analysis['kl_divergence']
        if kl_div < 0.1:
            binary_score = 20
        elif kl_div < 0.5:
            binary_score = 15
        elif kl_div < 1.0:
            binary_score = 10
        else:
            binary_score = 5
        
        consistency_score += binary_score
        print(f"🔢 二进制分布相似性 ({binary_score}/20分)")
        
        # 6. 熵相似性
        entropy1, entropy2 = binary_analysis['entropy']
        entropy_diff = abs(entropy1 - entropy2)
        
        if entropy_diff < 0.1:
            entropy_score = 20
        elif entropy_diff < 0.3:
            entropy_score = 15
        elif entropy_diff < 0.5:
            entropy_score = 10
        else:
            entropy_score = 5
        
        consistency_score += entropy_score
        print(f"📊 信息熵相似性 ({entropy_score}/20分)")
    
    print("\n" + "=" * 80)
    print(f"🎯 总体一致性评分: {consistency_score}/100")
    
    if consistency_score == 100:
        print("🎉 完全一致！文件完全相同！")
    elif consistency_score >= 90:
        print("✅ 高度一致！格式和特征几乎完全匹配！")
    elif consistency_score >= 80:
        print("✅ 基本一致！主要特征匹配，应该能正常播放！")
    elif consistency_score >= 70:
        print("⚠️  部分一致！有一些差异，可能影响播放！")
    else:
        print("❌ 差异较大！可能无法正常播放！")
    
    print("=" * 80)

def read_p3_packets(file_path):
    """
    读取P3文件中的所有数据包
    """
    packets = []
    
    with open(file_path, 'rb') as f:
        while True:
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
                
            packets.append(packet_data)
    
    return packets

def main():
    if len(sys.argv) != 3:
        print("使用方法: python deep_analysis_p3.py <999.p3> <转换的p3文件>")
        sys.exit(1)
    
    file1 = sys.argv[1]  # 999.p3
    file2 = sys.argv[2]  # 我们转换的文件
    
    if not os.path.exists(file1):
        print(f"文件不存在: {file1}")
        sys.exit(1)
    
    if not os.path.exists(file2):
        print(f"文件不存在: {file2}")
        sys.exit(1)
    
    deep_comprehensive_analysis(file1, file2)

if __name__ == "__main__":
    main()
