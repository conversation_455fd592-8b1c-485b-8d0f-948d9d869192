#ifndef NETWORK_CONSOLE_H
#define NETWORK_CONSOLE_H

#include <esp_console.h>
#include <esp_log.h>
#include <string>

// 前向声明
namespace communication {
    class UartCommandManager;
}

// C接口函数声明，供汽车语音系统调用
#ifdef __cplusplus
extern "C" {
#endif

void network_console_process_uart_data(const char* data, int len);

#ifdef __cplusplus
}
#endif

class NetworkConsole {
public:
    static NetworkConsole& GetInstance();
    
    void Initialize();
    void RegisterNetworkCommands();

    /**
     * @brief 初始化UART指令管理器
     */
    void InitializeUartCommandManager();

    /**
     * @brief 处理UART数据（用于UART指令模式）
     * @param data 接收到的数据
     */
    void ProcessUartData(const std::string& data);
    
private:
    NetworkConsole() = default;
    ~NetworkConsole() = default;
    
    // 禁止拷贝和赋值
    NetworkConsole(const NetworkConsole&) = delete;
    NetworkConsole& operator=(const NetworkConsole&) = delete;
    
    // 命令处理函数
    static int CmdSwitchTo4G(int argc, char** argv);
    static int CmdSwitchToWiFi(int argc, char** argv);
    static int CmdNetworkStatus(int argc, char** argv);
    static int CmdHelp(int argc, char** argv);
    static int CmdReboot(int argc, char** argv);
    static int CmdLocalAudio(int argc, char** argv);
    static int CmdAudioStatus(int argc, char** argv);
    static int CmdAudioSave(int argc, char** argv);
    static int CmdTtsList(int argc, char** argv);
    static int CmdTtsPlay(int argc, char** argv);
    static int CmdTtsClear(int argc, char** argv);
    static int CmdTtsExport(int argc, char** argv);
    static int CmdTtsExportHex(int argc, char** argv);
    static int CmdTtsExportAll(int argc, char** argv);

    // 指令配置管理命令 - 暂时注释掉
    // static int CmdConfigStatus(int argc, char** argv);



    /**
     * @brief 设置全局UART命令管理器指针
     * @param manager UART命令管理器指针
     */
    void SetGlobalUartCommandManager(communication::UartCommandManager* manager);

    bool initialized_ = false;
    communication::UartCommandManager* uart_command_manager_ = nullptr;
};

#endif // NETWORK_CONSOLE_H
