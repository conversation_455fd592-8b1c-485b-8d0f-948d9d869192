360通用机串口通信协议文本整理版
一、物理层描述
通信接口：标准UART接口

逻辑电平：3.3V/5V TTL电平
工作模式：8N1模式（8位数据位，无奇偶校验位，1位停止位）
波特率：固定19200bps
二、链路层描述
设备角色约定：
HOST：360全景主机
SLAVE：总线解码器
数据帧结构（按传输顺序）： 第1字节：Head Code（固定为0x2E） 第2字节：Data Type（数据类型标识） 第3字节：Length（数据长度） 第4字节：Data0（数据内容起始） ... 第N字节：DataN（数据内容结束） 最后1字节：Checksum（校验和）
校验和计算方法： SUM(DataType + Length + Data0 + ... + DataN) ^ 0xFF

三、应用层描述
DataType定义：
Slave→Host方向： 0x02：按键信息 0x03：车身信息 0x04：时间信息 0x05：触摸信息 0x09：语音控制
Host→Slave方向： （保留未定义）
四、数据格式详解
2.2 按键信息【0x02】
数据结构：

DataType：0x02（固定）
Length：0x02（固定2字节数据）
数据内容： Data0：按键值 0x04：MENU键 0x05：返回键 0x10：旋钮确认 0x11：旋钮上 0x12：旋钮下 0x13：旋钮左 0x14：旋钮右 0x19：旋钮左旋 0x1A：旋钮右旋 0x40：唤醒/关闭360 0xF0：关屏（语音控制：关闭全景） 0xF1：开屏（语音控制：打开全景） 0xF2：开屏

Data1：按键状态 当按键值为0x19/0x1A/0xF0/0xF1/0xF2时： 固定发送0x00（无按下状态） 其他按键值： 0x00：按键释放/无按键 0x01：按键按下

2.3 车身信息【0x03】
数据结构：

DataType：0x03（固定）
Length：0x08（标准协议）或0x10（四为补充协议）
标准协议（8字节）： 
Data0：基本状态 Bit0~1：ACC状态 00：钥匙拔出（ACC输出0V） 01：ACC OFF（ACC输出0V） 10：ACC（ACC输出12V） 11：ACC ON（ACC输出12V） Bit2：ILL状态（0：关闭，1：开启） Bit3：脚刹状态（0：正常，1：刹车） Bit4~7：档位状态 0000：P档 0001：R档 0010：N档 0011：D档

Data1：车灯/油门状态 Bit0：左转向灯（0：关闭，1：打开） Bit1：右转向灯（0：关闭，1：打开） Bit2：双闪灯（0：关闭，1：打开） Bit3：远光灯（0：关闭，1：打开） Bit4~7：油门状态 0x00：正常 0x01~0x0F：轻踩到重踩

Data2：车速（0~255 km/h） 

Data3：方向盘转角（0x00最左~0x80中间~0xFF最右） 

Data4：前雷达状态（每2bit表示一个雷达状态） 

Data5：后雷达状态（每2bit表示一个雷达状态） 

Data6：车门状态/P键 

Data7：发动机转速（值×64 RPM）

四为补充协议（16字节）： 在标准协议基础上增加： Data8：雷达模式（固定0xB7） Data9~16：雷达数据（每字节包含2个雷达的报警段数）

2.4 车身时间信息【0x04】
数据结构：

DataType：0x04（固定）
Length：0x07（固定7字节数据）
数据内容： Data0：显示控制 Bit7~4：日期格式（保留） Bit3~0：保留

Data1：年（0~99，如0x10表示2016年） Data2：月（0~12，如0x0A表示10月） Data3：日（0~31，如0x17表示23日） Data4：时（0~23，24进制） Data5：分（0~59） Data6：秒（0~59）

2.7 语音控制命令【0x09】
数据结构：

DataType：0x09（固定）
Length：0x01（固定1字节数据）
数据内容： Data0：界面状态 0x01：打开前视 0x02：打开后视 0x03：打开左视 0x04：打开右视 0x16：打开窄道模式（V3.04+支持） 0x18：打开路崖模式（V3.04+支持） 0x25：前流媒体（V3.04+支持） 0x26：后流媒体（V3.04+支持） 0x40：打开2D（V3.04+支持） 0x41：打开3D（V3.04+支持）

注：打开/关闭全景命令见按键信息中的0xF1/0xF0