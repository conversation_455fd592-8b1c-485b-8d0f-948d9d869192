#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智TTS音频导出工具
从ESP32设备导出保存的TTS音频文件到电脑
"""

import serial
import os
import time
import re
from pathlib import Path

class TTSAudioExporter:
    """TTS音频导出器"""
    
    def __init__(self, port='COM13', baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.output_dir = Path("J:/xiaozhi-esp32/audio_files")
        self.output_dir.mkdir(exist_ok=True)
        
    def connect_to_device(self):
        """连接到ESP32设备"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=2)
            print(f"✅ 已连接到设备: {self.port}")
            time.sleep(1)  # 等待连接稳定
            return True
        except Exception as e:
            print(f"❌ 连接设备失败: {e}")
            return False
    
    def send_command(self, command):
        """发送命令到设备"""
        try:
            self.ser.write(f"{command}\n".encode())
            time.sleep(0.5)  # 等待命令执行
        except Exception as e:
            print(f"❌ 发送命令失败: {e}")
    
    def read_response(self, timeout=5):
        """读取设备响应"""
        response = ""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.ser.in_waiting > 0:
                data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                response += data
                
                # 检查是否收到完整响应
                if "xiaozhi>" in response:
                    break
            time.sleep(0.1)
        
        return response
    
    def get_tts_file_list(self):
        """获取TTS文件列表"""
        print("📋 获取TTS文件列表...")
        
        self.send_command("tts_list")
        response = self.read_response()
        
        # 解析文件列表
        files = []
        lines = response.split('\n')
        for line in lines:
            # 匹配格式: "1. tts_001.p3 (37662 bytes)"
            match = re.search(r'(\d+)\.\s+(tts_\d+\.p3)\s+\((\d+)\s+bytes\)', line)
            if match:
                number = int(match.group(1))
                filename = match.group(2)
                size = int(match.group(3))
                files.append({
                    'number': number,
                    'filename': filename,
                    'size': size
                })
        
        return files
    
    def export_file_hex(self, filename):
        """导出单个文件的十六进制数据"""
        print(f"📤 导出文件: {filename}")
        
        self.send_command(f"tts_export_hex {filename}")
        response = self.read_response(timeout=10)
        
        # 提取十六进制数据
        hex_data = []
        lines = response.split('\n')
        in_hex_section = False
        
        for line in lines:
            if "--- HEX DATA START ---" in line:
                in_hex_section = True
                continue
            elif "--- HEX DATA END ---" in line:
                break
            elif in_hex_section:
                # 匹配格式: "00000000: 00 00 00 9A 58 A2 F3 E4 ..."
                match = re.search(r'^[0-9A-F]{8}:\s+([0-9A-F\s]+)\s+\|', line)
                if match:
                    hex_bytes = match.group(1).split()
                    hex_data.extend(hex_bytes)
        
        return hex_data
    
    def save_hex_to_file(self, filename, hex_data):
        """将十六进制数据保存为文件"""
        output_file = self.output_dir / filename
        
        try:
            with open(output_file, 'wb') as f:
                for hex_byte in hex_data:
                    if hex_byte.strip():  # 跳过空字符串
                        byte_value = int(hex_byte, 16)
                        f.write(bytes([byte_value]))
            
            file_size = output_file.stat().st_size
            print(f"✅ 文件已保存: {output_file} ({file_size} 字节)")
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def export_all_files(self):
        """导出所有TTS文件"""
        if not self.connect_to_device():
            return False
        
        try:
            # 获取文件列表
            files = self.get_tts_file_list()
            
            if not files:
                print("📭 没有找到TTS文件")
                return True
            
            print(f"📁 找到 {len(files)} 个TTS文件:")
            for file_info in files:
                print(f"   {file_info['number']}. {file_info['filename']} ({file_info['size']} 字节)")
            
            print(f"\n📂 导出目录: {self.output_dir}")
            
            # 逐个导出文件
            success_count = 0
            for file_info in files:
                filename = file_info['filename']
                
                # 导出十六进制数据
                hex_data = self.export_file_hex(filename)
                
                if hex_data:
                    # 保存为文件
                    if self.save_hex_to_file(filename, hex_data):
                        success_count += 1
                    else:
                        print(f"❌ 保存失败: {filename}")
                else:
                    print(f"❌ 导出失败: {filename}")
                
                time.sleep(1)  # 避免命令过快
            
            print(f"\n🎉 导出完成! 成功: {success_count}/{len(files)}")
            return True
            
        except Exception as e:
            print(f"❌ 导出过程出错: {e}")
            return False
        
        finally:
            if hasattr(self, 'ser'):
                self.ser.close()
                print("🔌 设备连接已关闭")
    
    def test_exported_files(self):
        """测试导出的文件"""
        print("\n🧪 测试导出的文件...")
        
        p3_files = list(self.output_dir.glob("tts_*.p3"))
        if not p3_files:
            print("❌ 没有找到导出的P3文件")
            return
        
        for p3_file in sorted(p3_files):
            size = p3_file.stat().st_size
            print(f"📄 {p3_file.name}: {size} 字节")
            
            # 简单验证P3格式
            try:
                with open(p3_file, 'rb') as f:
                    header = f.read(4)
                    if len(header) >= 4:
                        packet_type = header[0]
                        reserved = header[1]
                        payload_size = (header[2] << 8) | header[3]
                        print(f"   P3格式: type={packet_type}, size={payload_size}")
                    else:
                        print("   ⚠️  文件太小，可能不是有效的P3格式")
            except Exception as e:
                print(f"   ❌ 读取文件出错: {e}")

def main():
    """主函数"""
    print("🎵 小智TTS音频导出工具")
    print("=" * 50)
    
    # 创建导出器
    exporter = TTSAudioExporter()
    
    # 导出所有文件
    if exporter.export_all_files():
        # 测试导出的文件
        exporter.test_exported_files()
        
        print("\n💡 使用说明:")
        print("1. 导出的文件保存在: J:/xiaozhi-esp32/audio_files/")
        print("2. 可以将这些文件复制到小智设备的spiffs_image目录")
        print("3. 重新编译烧录后可以使用 'local' 命令播放")
    else:
        print("❌ 导出失败")

if __name__ == "__main__":
    main()
