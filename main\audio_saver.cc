#include "audio_saver.h"
#include <esp_log.h>
#include <esp_spiffs.h>
#include <sys/stat.h>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <cstring>
#include <arpa/inet.h>

#define TAG "AudioSaver"

AudioSaver::AudioSaver()
    : enabled_(false)  // 默认关闭音频保存功能
    , session_active_(false)
    , file_counter_(1)
{
    ESP_LOGI(TAG, "音频保存器已初始化 (默认关闭)");
}

AudioSaver::~AudioSaver() {
    if (session_active_) {
        EndSession();
    }
}

void AudioSaver::SetEnabled(bool enabled) {
    std::lock_guard<std::mutex> lock(mutex_);
    enabled_ = enabled;
    ESP_LOGI(TAG, "音频保存功能 %s", enabled ? "已启用" : "已禁用");
}

bool AudioSaver::IsEnabled() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return enabled_;
}

void AudioSaver::StartSession() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!enabled_) {
        return;
    }
    
    if (session_active_) {
        ESP_LOGW(TAG, "音频保存会话已在进行中");
        return;
    }
    
    session_active_ = true;
    current_audio_data_.clear();
    ESP_LOGI(TAG, "开始音频保存会话");
}

void AudioSaver::SaveAudioPacket(const AudioStreamPacket& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!enabled_ || !session_active_) {
        return;
    }
    
    try {
        // 构建BinaryProtocol3格式的数据包
        struct BinaryProtocol3 {
            uint8_t type;
            uint8_t reserved;
            uint16_t payload_size;
        } __attribute__((packed));
        
        BinaryProtocol3 header;
        header.type = 0;  // OPUS类型
        header.reserved = 0;
        header.payload_size = htons(packet.payload.size());
        
        // 添加头部
        size_t header_size = sizeof(BinaryProtocol3);
        size_t old_size = current_audio_data_.size();
        current_audio_data_.resize(old_size + header_size + packet.payload.size());
        
        // 复制头部
        memcpy(current_audio_data_.data() + old_size, &header, header_size);
        
        // 复制音频数据
        memcpy(current_audio_data_.data() + old_size + header_size, 
               packet.payload.data(), packet.payload.size());
        
        ESP_LOGD(TAG, "保存音频包: %d字节, 总计: %d字节", 
                packet.payload.size(), current_audio_data_.size());
                
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "保存音频包时出错: %s", e.what());
    } catch (...) {
        ESP_LOGE(TAG, "保存音频包时出现未知错误");
    }
}

std::string AudioSaver::EndSession() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!enabled_ || !session_active_) {
        return "";
    }
    
    session_active_ = false;
    
    if (current_audio_data_.empty()) {
        ESP_LOGW(TAG, "没有音频数据需要保存");
        return "";
    }
    
    try {
        std::string filename = GenerateFileName();
        
        if (SaveToFile(filename, current_audio_data_)) {
            last_saved_file_ = filename;
            file_counter_++;
            
            ESP_LOGI(TAG, "✅ TTS音频已保存: %s (%d字节)",
                    filename.c_str(), current_audio_data_.size());

            current_audio_data_.clear();
            return filename;
        } else {
            ESP_LOGE(TAG, "保存音频文件失败: %s", filename.c_str());
            current_audio_data_.clear();
            return "";
        }
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "结束音频会话时出错: %s", e.what());
        current_audio_data_.clear();
        return "";
    } catch (...) {
        ESP_LOGE(TAG, "结束音频会话时出现未知错误");
        current_audio_data_.clear();
        return "";
    }
}

AudioSaver::Status AudioSaver::GetStatus() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    Status status;
    status.enabled = enabled_;
    status.session_active = session_active_;
    status.current_packets = current_audio_data_.size() / 100;  // 估算包数
    status.file_counter = file_counter_;
    status.last_saved_file = last_saved_file_;
    
    return status;
}

void AudioSaver::ResetCounter() {
    std::lock_guard<std::mutex> lock(mutex_);
    file_counter_ = 1;
    last_saved_file_.clear();
    ESP_LOGI(TAG, "音频保存器计数器已重置");
}

std::string AudioSaver::GenerateFileName() {
    // 生成文件名: tts_001.p3, tts_002.p3, ...
    // 使用 /audio 目录，这是小智系统中音频文件的标准位置
    std::ostringstream oss;
    oss << "/audio/tts_" << std::setfill('0') << std::setw(3) << file_counter_ << ".p3";
    return oss.str();
}

bool AudioSaver::SaveToFile(const std::string& filename, const std::vector<uint8_t>& data) {
    try {
        FILE* file = fopen(filename.c_str(), "wb");
        if (!file) {
            ESP_LOGE(TAG, "无法创建文件: %s", filename.c_str());
            return false;
        }
        
        size_t written = fwrite(data.data(), 1, data.size(), file);
        fclose(file);
        
        if (written != data.size()) {
            ESP_LOGE(TAG, "写入文件不完整: %s (写入%d/%d字节)", 
                    filename.c_str(), written, data.size());
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "保存文件时出错: %s", e.what());
        return false;
    } catch (...) {
        ESP_LOGE(TAG, "保存文件时出现未知错误");
        return false;
    }
}

// 全局实例
static AudioSaver g_audio_saver;

AudioSaver& GetAudioSaver() {
    return g_audio_saver;
}

// 安全的对外接口函数
void SafeStartAudioSession() {
    try {
        GetAudioSaver().StartSession();
    } catch (...) {
        // 静默处理错误，不影响正常功能
    }
}

void SafeSaveAudioPacket(const AudioStreamPacket& packet) {
    try {
        GetAudioSaver().SaveAudioPacket(packet);
    } catch (...) {
        // 静默处理错误，不影响正常功能
    }
}

std::string SafeEndAudioSession() {
    try {
        return GetAudioSaver().EndSession();
    } catch (...) {
        // 静默处理错误，不影响正常功能
        return "";
    }
}
