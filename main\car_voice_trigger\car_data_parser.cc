#include "car_data_parser.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <string.h>

static const char* TAG = "CarDataParser";

esp_err_t car_data_parser_init(car_data_parser_t* parser) {
    if (!parser) {
        return ESP_ERR_INVALID_ARG;
    }

    memset(parser, 0, sizeof(car_data_parser_t));
    parser->state = PARSER_STATE_IDLE;
    parser->protocol_type = CAR_PROTOCOL_AUTO_DETECT;

    ESP_LOGI(TAG, "Car data parser initialized");
    return ESP_OK;
}

void car_data_parser_reset(car_data_parser_t* parser) {
    if (!parser) {
        return;
    }

    parser->state = PARSER_STATE_IDLE;
    parser->payload_index = 0;
    parser->expected_checksum = 0;
    memset(&parser->current_packet, 0, sizeof(car_data_packet_t));
}

void car_data_parser_set_protocol(car_data_parser_t* parser, uint8_t protocol_type) {
    if (!parser) {
        return;
    }

    parser->protocol_type = protocol_type;
    ESP_LOGI(TAG, "Protocol set to: %d", protocol_type);
}

parse_result_t car_data_parser_feed(car_data_parser_t* parser, uint8_t byte) {
    if (!parser) {
        return PARSE_RESULT_ERROR;
    }

    switch (parser->state) {
        case PARSER_STATE_IDLE:
        case PARSER_STATE_HEADER:
            // 检查包头
            if (byte == CAR_PACKET_HEADER_360 || byte == CAR_PACKET_HEADER_CUSTOM) {
                parser->current_packet.header = byte;
                parser->state = PARSER_STATE_TYPE;
                
                // 自动检测协议类型
                if (parser->protocol_type == CAR_PROTOCOL_AUTO_DETECT) {
                    if (byte == CAR_PACKET_HEADER_360) {
                        parser->protocol_type = CAR_PROTOCOL_360;
                    } else {
                        parser->protocol_type = CAR_PROTOCOL_CUSTOM;
                    }
                }
                
                return PARSE_RESULT_INCOMPLETE;
            } else {
                parser->state = PARSER_STATE_IDLE;
                return PARSE_RESULT_INVALID_HEADER;
            }

        case PARSER_STATE_TYPE:
            parser->current_packet.data_type = byte;
            parser->state = PARSER_STATE_LENGTH;
            return PARSE_RESULT_INCOMPLETE;

        case PARSER_STATE_LENGTH:
            if (byte > sizeof(parser->current_packet.payload)) {
                car_data_parser_reset(parser);
                parser->error_count++;
                return PARSE_RESULT_INVALID_LENGTH;
            }
            parser->current_packet.length = byte;
            parser->payload_index = 0;
            
            if (byte == 0) {
                parser->state = PARSER_STATE_CHECKSUM;
            } else {
                parser->state = PARSER_STATE_PAYLOAD;
            }
            return PARSE_RESULT_INCOMPLETE;

        case PARSER_STATE_PAYLOAD:
            parser->current_packet.payload[parser->payload_index++] = byte;
            
            if (parser->payload_index >= parser->current_packet.length) {
                parser->state = PARSER_STATE_CHECKSUM;
            }
            return PARSE_RESULT_INCOMPLETE;

        case PARSER_STATE_CHECKSUM:
            parser->current_packet.checksum = byte;
            
            // 验证校验和
            if (car_data_verify_checksum(&parser->current_packet)) {
                parser->state = PARSER_STATE_COMPLETE;
                parser->packet_count++;
                parser->last_valid_time = esp_timer_get_time() / 1000;
                return PARSE_RESULT_OK;
            } else {
                car_data_parser_reset(parser);
                parser->error_count++;
                return PARSE_RESULT_CHECKSUM_ERROR;
            }

        case PARSER_STATE_COMPLETE:
            // 重置状态，准备接收下一个包
            car_data_parser_reset(parser);
            // 递归处理当前字节
            return car_data_parser_feed(parser, byte);

        default:
            car_data_parser_reset(parser);
            return PARSE_RESULT_ERROR;
    }
}

parse_result_t car_data_parser_feed_buffer(car_data_parser_t* parser, const uint8_t* data, size_t len) {
    if (!parser || !data || len == 0) {
        return PARSE_RESULT_ERROR;
    }

    parse_result_t last_result = PARSE_RESULT_INCOMPLETE;
    
    for (size_t i = 0; i < len; i++) {
        parse_result_t result = car_data_parser_feed(parser, data[i]);
        
        if (result == PARSE_RESULT_OK) {
            last_result = result;
        } else if (result != PARSE_RESULT_INCOMPLETE) {
            last_result = result;
        }
    }

    return last_result;
}

bool car_data_parser_has_packet(const car_data_parser_t* parser) {
    return parser && parser->state == PARSER_STATE_COMPLETE;
}

car_data_packet_t car_data_parser_get_packet(car_data_parser_t* parser) {
    car_data_packet_t packet = {0};
    
    if (parser && parser->state == PARSER_STATE_COMPLETE) {
        packet = parser->current_packet;
        car_data_parser_reset(parser);
    }

    return packet;
}

bool car_data_update_vehicle_state(const car_data_packet_t* packet, vehicle_state_t* state) {
    if (!packet || !state) {
        return false;
    }

    // 根据协议类型解析数据
    bool result = false;
    
    if (packet->header == CAR_PACKET_HEADER_360) {
        result = car_data_parse_360_protocol(packet, state);
    } else if (packet->header == CAR_PACKET_HEADER_CUSTOM) {
        result = car_data_parse_custom_protocol(packet, state);
    }

    if (result) {
        state->data_valid = true;
        state->last_update_time = esp_timer_get_time() / 1000;
    }

    return result;
}

bool car_data_parse_360_protocol(const car_data_packet_t* packet, vehicle_state_t* state) {
    if (!packet || !state || packet->length < 8) {
        return false;
    }

    // 360协议数据格式解析 (根据360通用协议文档)
    const uint8_t* data = packet->payload;

    switch (packet->data_type) {
        case CAR_DATA_TYPE_VEHICLE_INFO:
            // 车身信息 (DataType=0x03, Length=0x08)
            // Data0: 基本状态 (ACC/ILL/脚刹/档位)
            state->acc_status = (data[0] & 0x03);  // Bit0~1: ACC状态
            state->gear_position = (data[0] & 0xF0) >> 4;  // Bit4~7: 档位状态

            // Data1: 车灯/油门状态
            state->turn_signals.left_signal = (data[1] & 0x01) ? true : false;   // Bit0: 左转向灯
            state->turn_signals.right_signal = (data[1] & 0x02) ? true : false;  // Bit1: 右转向灯

            // Data2: 车速 (0~255 km/h)
            state->vehicle_speed = data[2];

            // Data3: 方向盘转角 (0x00最左~0x80中间~0xFF最右)
            state->steering_angle = data[3];

            // Data4: 前雷达状态 (暂不处理)
            // Data5: 后雷达状态 (暂不处理)

            // Data6: 车门状态/P键
            state->doors.driver_door = (data[6] & 0x01) ? true : false;      // Bit0: 主驾车门
            state->doors.passenger_door = (data[6] & 0x02) ? true : false;   // Bit1: 副驾车门
            state->doors.rear_left_door = (data[6] & 0x04) ? true : false;   // Bit2: 左后车门
            state->doors.rear_right_door = (data[6] & 0x08) ? true : false;  // Bit3: 右后车门

            // Data7: 发动机转速 (值×64 RPM, 暂不处理)
            break;

        case CAR_DATA_TYPE_SPEED:
            state->vehicle_speed = (data[0] << 8) | data[1];
            break;

        case CAR_DATA_TYPE_DOOR:
            state->doors.driver_door = (data[0] & 0x01) ? true : false;
            state->doors.passenger_door = (data[0] & 0x02) ? true : false;
            state->doors.rear_left_door = (data[0] & 0x04) ? true : false;
            state->doors.rear_right_door = (data[0] & 0x08) ? true : false;
            break;

        case CAR_DATA_TYPE_GEAR:
            state->gear_position = data[0] & 0x03;
            break;

        case CAR_DATA_TYPE_SIGNAL:
            state->turn_signals.left_signal = (data[0] & 0x01) ? true : false;
            state->turn_signals.right_signal = (data[0] & 0x02) ? true : false;
            break;

        case CAR_DATA_TYPE_STEERING:
            state->steering_angle = data[0];
            break;

        default:
            return false;
    }

    return true;
}

bool car_data_parse_custom_protocol(const car_data_packet_t* packet, vehicle_state_t* state) {
    if (!packet || !state || packet->length < 4) {
        return false;
    }

    // 自定义协议数据格式解析
    // 这里是示例实现，可以根据实际协议调整
    const uint8_t* data = packet->payload;
    
    // 简单的自定义协议：所有数据在一个包中
    if (packet->data_type == CAR_DATA_TYPE_STATUS && packet->length >= 8) {
        state->acc_status = data[0];
        state->gear_position = data[1];
        state->vehicle_speed = (data[2] << 8) | data[3];
        
        uint8_t door_status = data[4];
        state->doors.driver_door = (door_status & 0x01) ? true : false;
        state->doors.passenger_door = (door_status & 0x02) ? true : false;
        state->doors.rear_left_door = (door_status & 0x04) ? true : false;
        state->doors.rear_right_door = (door_status & 0x08) ? true : false;
        
        uint8_t signal_status = data[5];
        state->turn_signals.left_signal = (signal_status & 0x01) ? true : false;
        state->turn_signals.right_signal = (signal_status & 0x02) ? true : false;
        
        state->steering_angle = data[6];
        
        return true;
    }

    return false;
}

uint8_t car_data_calculate_checksum(const uint8_t* data, size_t len) {
    if (!data || len < 3) {  // 至少需要data_type + length + 至少1字节数据
        return 0;
    }

    // 360协议校验和算法：SUM(DataType + Length + Data0 + ... + DataN) ^ 0xFF
    // data[0] = data_type, data[1] = length, data[2...] = payload
    uint8_t data_type = data[0];
    uint8_t length = data[1];

    uint8_t sum = data_type + length;

    // 添加payload数据（从data[2]开始，共length字节）
    for (uint8_t i = 0; i < length && (i + 2) < len; i++) {
        sum += data[i + 2];
    }

    return sum ^ 0xFF;
}

bool car_data_verify_checksum(const car_data_packet_t* packet) {
    if (!packet) {
        return false;
    }

    // 360协议校验和算法：SUM(DataType + Length + Data0 + ... + DataN) ^ 0xFF
    // 注意：不包含Header(0x2E)
    uint8_t sum = packet->data_type + packet->length;

    for (uint8_t i = 0; i < packet->length; i++) {
        sum += packet->payload[i];
    }

    uint8_t calculated_checksum = sum ^ 0xFF;

    ESP_LOGD(TAG, "🚗🔍 Checksum verification: sum=0x%02X, calculated=0x%02X, received=0x%02X",
             sum, calculated_checksum, packet->checksum);

    return calculated_checksum == packet->checksum;
}

uint8_t car_data_detect_protocol(const uint8_t* data, size_t len) {
    if (!data || len < 3) {
        return CAR_PROTOCOL_AUTO_DETECT;
    }

    // 检查包头
    if (data[0] == CAR_PACKET_HEADER_360) {
        return CAR_PROTOCOL_360;
    } else if (data[0] == CAR_PACKET_HEADER_CUSTOM) {
        return CAR_PROTOCOL_CUSTOM;
    }

    return CAR_PROTOCOL_AUTO_DETECT;
}

void car_data_parser_print_stats(const car_data_parser_t* parser) {
    if (!parser) {
        return;
    }

    ESP_LOGI(TAG, "=== Parser Statistics ===");
    ESP_LOGI(TAG, "State: %d", parser->state);
    ESP_LOGI(TAG, "Protocol: %d", parser->protocol_type);
    ESP_LOGI(TAG, "Packets: %lu", parser->packet_count);
    ESP_LOGI(TAG, "Errors: %lu", parser->error_count);
    ESP_LOGI(TAG, "Last Valid: %lu ms", parser->last_valid_time);

    if (parser->packet_count > 0) {
        float error_rate = (float)parser->error_count / (parser->packet_count + parser->error_count) * 100.0f;
        ESP_LOGI(TAG, "Error Rate: %.2f%%", error_rate);
    }
}

void car_data_packet_print(const car_data_packet_t* packet) {
    if (!packet) {
        return;
    }

    ESP_LOGI(TAG, "=== Packet Data ===");
    ESP_LOGI(TAG, "Header: 0x%02X", packet->header);
    ESP_LOGI(TAG, "Type: 0x%02X", packet->data_type);
    ESP_LOGI(TAG, "Length: %d", packet->length);
    ESP_LOGI(TAG, "Checksum: 0x%02X", packet->checksum);

    if (packet->length > 0) {
        ESP_LOG_BUFFER_HEX(TAG, packet->payload, packet->length);
    }
}
