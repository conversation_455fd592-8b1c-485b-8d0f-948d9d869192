#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专注于P3文件格式和参数的一致性分析
不关注文件大小和内容，只关注格式规范
"""

import struct
import os
import sys

def analyze_format_parameters(file1, file2):
    """
    分析两个P3文件的格式和参数一致性
    """
    print("🔍 P3文件格式和参数一致性分析")
    print("=" * 80)
    print(f"文件1 (小智原版): {file1}")
    print(f"文件2 (我们转换): {file2}")
    print("=" * 80)
    
    # 读取两个文件的数据包
    packets1 = read_p3_packets_with_headers(file1)
    packets2 = read_p3_packets_with_headers(file2)
    
    print(f"📦 数据包数量: {len(packets1)} vs {len(packets2)}")
    print()
    
    # 1. P3头部格式一致性检查
    check_p3_header_format(packets1, packets2)
    
    # 2. Opus参数一致性检查
    check_opus_parameters(packets1, packets2)
    
    # 3. P3格式规范符合性检查
    check_p3_format_compliance(packets1, packets2)
    
    # 4. 生成格式一致性报告
    generate_format_compliance_report(packets1, packets2)

def read_p3_packets_with_headers(file_path):
    """
    读取P3文件，包含头部信息
    """
    packets = []
    
    with open(file_path, 'rb') as f:
        packet_index = 0
        while True:
            # 读取4字节头部
            header = f.read(4)
            if not header or len(header) < 4:
                break
            
            # 解析头部
            packet_type, reserved, data_len = struct.unpack('>BBH', header)
            
            # 读取数据包
            packet_data = f.read(data_len)
            if len(packet_data) != data_len:
                break
            
            packets.append({
                'index': packet_index,
                'header': {
                    'packet_type': packet_type,
                    'reserved': reserved,
                    'data_len': data_len
                },
                'data': packet_data
            })
            
            packet_index += 1
    
    return packets

def check_p3_header_format(packets1, packets2):
    """
    检查P3头部格式一致性
    """
    print("📋 1. P3头部格式检查")
    print("-" * 50)
    
    # 检查头部字段的一致性
    header_issues = []
    
    # 检查packet_type字段
    types1 = [p['header']['packet_type'] for p in packets1]
    types2 = [p['header']['packet_type'] for p in packets2]
    
    unique_types1 = set(types1)
    unique_types2 = set(types2)
    
    print(f"数据包类型:")
    print(f"  文件1: {unique_types1}")
    print(f"  文件2: {unique_types2}")
    
    if unique_types1 == unique_types2:
        print("  ✅ 数据包类型一致")
    else:
        print("  ❌ 数据包类型不一致")
        header_issues.append("数据包类型不一致")
    
    # 检查reserved字段
    reserved1 = [p['header']['reserved'] for p in packets1]
    reserved2 = [p['header']['reserved'] for p in packets2]
    
    unique_reserved1 = set(reserved1)
    unique_reserved2 = set(reserved2)
    
    print(f"保留字段:")
    print(f"  文件1: {unique_reserved1}")
    print(f"  文件2: {unique_reserved2}")
    
    if unique_reserved1 == unique_reserved2:
        print("  ✅ 保留字段一致")
    else:
        print("  ❌ 保留字段不一致")
        header_issues.append("保留字段不一致")
    
    # 检查长度字段的合理性
    lengths1 = [p['header']['data_len'] for p in packets1]
    lengths2 = [p['header']['data_len'] for p in packets2]
    
    print(f"数据长度字段:")
    print(f"  文件1范围: {min(lengths1)}-{max(lengths1)} 字节")
    print(f"  文件2范围: {min(lengths2)}-{max(lengths2)} 字节")
    
    # 检查长度字段是否与实际数据匹配
    length_mismatch1 = sum(1 for p in packets1 if p['header']['data_len'] != len(p['data']))
    length_mismatch2 = sum(1 for p in packets2 if p['header']['data_len'] != len(p['data']))
    
    print(f"长度字段准确性:")
    print(f"  文件1不匹配: {length_mismatch1} 个数据包")
    print(f"  文件2不匹配: {length_mismatch2} 个数据包")
    
    if length_mismatch1 == 0 and length_mismatch2 == 0:
        print("  ✅ 长度字段完全准确")
    else:
        print("  ❌ 长度字段有误")
        header_issues.append("长度字段不准确")
    
    return header_issues

def check_opus_parameters(packets1, packets2):
    """
    检查Opus编码参数一致性
    """
    print(f"\n🎵 2. Opus编码参数检查")
    print("-" * 50)
    
    opus_issues = []
    
    # 分析TOC字节
    def analyze_toc_bytes(packets, file_name):
        toc_analysis = {}
        
        for packet in packets:
            if len(packet['data']) > 0:
                toc = packet['data'][0]
                
                # 解析TOC字节
                config = (toc >> 3) & 0x1F
                stereo = (toc >> 2) & 0x01
                frame_count = toc & 0x03
                
                # 解析配置参数
                if config < 12:
                    mode = "SILK"
                    bandwidth_map = {0: "NB", 1: "MB", 2: "WB", 3: "SWB"}
                    bandwidth = bandwidth_map.get(config // 4, "Unknown")
                    frame_size_map = {0: 10, 1: 20, 2: 40, 3: 60}
                    frame_size = frame_size_map.get(config % 4, "Unknown")
                elif config < 16:
                    mode = "Hybrid"
                    bandwidth = "SWB" if (config - 12) // 2 == 0 else "FB"
                    frame_size = 10 if (config - 12) % 2 == 0 else 20
                else:
                    mode = "CELT"
                    bandwidth_map = {0: "NB", 1: "WB", 2: "SWB", 3: "FB"}
                    bandwidth = bandwidth_map.get((config - 16) // 4, "Unknown")
                    frame_size_map = {0: 2.5, 1: 5, 2: 10, 3: 20}
                    frame_size = frame_size_map.get((config - 16) % 4, "Unknown")
                
                key = (toc, config, mode, bandwidth, frame_size, bool(stereo), frame_count)
                toc_analysis[key] = toc_analysis.get(key, 0) + 1
        
        return toc_analysis
    
    # 分析两个文件的TOC参数
    toc_analysis1 = analyze_toc_bytes(packets1, "文件1")
    toc_analysis2 = analyze_toc_bytes(packets2, "文件2")
    
    print("Opus配置参数分析:")
    
    def print_toc_analysis(analysis, file_name):
        print(f"\n{file_name}:")
        for (toc, config, mode, bandwidth, frame_size, stereo, frame_count), count in analysis.items():
            print(f"  TOC=0x{toc:02X}, Config={config}, Mode={mode}, BW={bandwidth}, "
                  f"Frame={frame_size}ms, Stereo={stereo}, Frames={frame_count} ({count}个数据包)")
    
    print_toc_analysis(toc_analysis1, "文件1 (小智原版)")
    print_toc_analysis(toc_analysis2, "文件2 (我们转换)")
    
    # 检查参数一致性
    print(f"\n参数一致性检查:")
    
    # 检查是否使用相同的配置
    configs1 = set(key[1] for key in toc_analysis1.keys())
    configs2 = set(key[1] for key in toc_analysis2.keys())
    
    print(f"  Opus配置: {configs1} vs {configs2}")
    if configs1 == configs2:
        print("  ✅ Opus配置一致")
    else:
        print("  ❌ Opus配置不一致")
        opus_issues.append("Opus配置不一致")
    
    # 检查编码模式
    modes1 = set(key[2] for key in toc_analysis1.keys())
    modes2 = set(key[2] for key in toc_analysis2.keys())
    
    print(f"  编码模式: {modes1} vs {modes2}")
    if modes1 == modes2:
        print("  ✅ 编码模式一致")
    else:
        print("  ❌ 编码模式不一致")
        opus_issues.append("编码模式不一致")
    
    # 检查带宽
    bandwidths1 = set(key[3] for key in toc_analysis1.keys())
    bandwidths2 = set(key[3] for key in toc_analysis2.keys())
    
    print(f"  带宽设置: {bandwidths1} vs {bandwidths2}")
    if bandwidths1 == bandwidths2:
        print("  ✅ 带宽设置一致")
    else:
        print("  ❌ 带宽设置不一致")
        opus_issues.append("带宽设置不一致")
    
    # 检查帧大小
    frame_sizes1 = set(key[4] for key in toc_analysis1.keys())
    frame_sizes2 = set(key[4] for key in toc_analysis2.keys())
    
    print(f"  帧大小: {frame_sizes1} vs {frame_sizes2}")
    if frame_sizes1 == frame_sizes2:
        print("  ✅ 帧大小一致")
    else:
        print("  ❌ 帧大小不一致")
        opus_issues.append("帧大小不一致")
    
    # 检查声道配置
    stereo1 = set(key[5] for key in toc_analysis1.keys())
    stereo2 = set(key[5] for key in toc_analysis2.keys())
    
    print(f"  声道配置: {stereo1} vs {stereo2}")
    if stereo1 == stereo2:
        print("  ✅ 声道配置一致")
    else:
        print("  ❌ 声道配置不一致")
        opus_issues.append("声道配置不一致")
    
    return opus_issues

def check_p3_format_compliance(packets1, packets2):
    """
    检查P3格式规范符合性
    """
    print(f"\n📏 3. P3格式规范符合性检查")
    print("-" * 50)
    
    compliance_issues = []
    
    def check_compliance(packets, file_name):
        issues = []
        
        print(f"\n{file_name}:")
        
        # 检查头部格式
        for packet in packets:
            header = packet['header']
            
            # 检查packet_type是否为0 (音频数据)
            if header['packet_type'] != 0:
                issues.append(f"数据包类型非0: {header['packet_type']}")
            
            # 检查reserved字段是否为0
            if header['reserved'] != 0:
                issues.append(f"保留字段非0: {header['reserved']}")
            
            # 检查数据长度是否合理 (Opus数据包通常64-200字节)
            if header['data_len'] < 1 or header['data_len'] > 1500:
                issues.append(f"数据长度异常: {header['data_len']}")
        
        # 检查Opus数据包格式
        for packet in packets:
            data = packet['data']
            if len(data) > 0:
                toc = data[0]
                config = (toc >> 3) & 0x1F
                
                # 检查配置是否在有效范围内
                if config > 31:
                    issues.append(f"Opus配置超出范围: {config}")
        
        if not issues:
            print("  ✅ 完全符合P3格式规范")
        else:
            print(f"  ❌ 发现 {len(issues)} 个格式问题")
            for issue in issues[:5]:  # 只显示前5个问题
                print(f"    - {issue}")
        
        return issues
    
    issues1 = check_compliance(packets1, "文件1 (小智原版)")
    issues2 = check_compliance(packets2, "文件2 (我们转换)")
    
    return issues1, issues2

def generate_format_compliance_report(packets1, packets2):
    """
    生成格式符合性综合报告
    """
    print(f"\n" + "=" * 80)
    print("📋 格式符合性综合报告")
    print("=" * 80)
    
    # 重新进行各项检查并计分
    header_issues = check_p3_header_format(packets1, packets2)
    opus_issues = check_opus_parameters(packets1, packets2)
    compliance_issues1, compliance_issues2 = check_p3_format_compliance(packets1, packets2)
    
    # 计算格式一致性得分
    format_score = 0
    total_checks = 6  # 总共6项检查
    
    # P3头部格式 (2分)
    if len(header_issues) == 0:
        format_score += 2
        print("✅ P3头部格式完全一致 (2/2分)")
    else:
        partial_score = max(0, 2 - len(header_issues))
        format_score += partial_score
        print(f"⚠️  P3头部格式部分一致 ({partial_score}/2分)")
    
    # Opus编码参数 (4分)
    if len(opus_issues) == 0:
        format_score += 4
        print("✅ Opus编码参数完全一致 (4/4分)")
    else:
        partial_score = max(0, 4 - len(opus_issues))
        format_score += partial_score
        print(f"⚠️  Opus编码参数部分一致 ({partial_score}/4分)")
    
    print(f"\n🎯 格式一致性总分: {format_score}/{total_checks}")
    
    # 生成结论
    if format_score == total_checks:
        print("🎉 格式完全一致！两个文件使用相同的P3格式和Opus参数！")
        print("✅ 应该能在小智系统上正常播放！")
    elif format_score >= total_checks * 0.8:
        print("✅ 格式基本一致！主要参数匹配，应该能正常播放！")
    elif format_score >= total_checks * 0.6:
        print("⚠️  格式部分一致！有一些参数差异，可能影响播放！")
    else:
        print("❌ 格式差异较大！可能无法正常播放！")
    
    print("=" * 80)

def main():
    if len(sys.argv) != 3:
        print("使用方法: python format_parameter_analysis.py <999.p3> <转换的p3文件>")
        sys.exit(1)
    
    file1 = sys.argv[1]  # 999.p3
    file2 = sys.argv[2]  # 我们转换的文件
    
    if not os.path.exists(file1):
        print(f"文件不存在: {file1}")
        sys.exit(1)
    
    if not os.path.exists(file2):
        print(f"文件不存在: {file2}")
        sys.exit(1)
    
    analyze_format_parameters(file1, file2)

if __name__ == "__main__":
    main()
