#ifndef PWM_AUDIO_TEST_H
#define PWM_AUDIO_TEST_H

#include <driver/gpio.h>
#include <driver/ledc.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <vector>

/**
 * 简单的PWM音频输出测试类
 * 用于验证音频数据流是否正常
 * 注意：这只是测试用途，音质较差
 */
class PwmAudioTest {
private:
    gpio_num_t pwm_gpio_;
    ledc_channel_t ledc_channel_;
    bool is_initialized_;
    bool is_playing_;
    
    static const char* TAG;
    static const uint32_t PWM_FREQUENCY = 20000;  // 20kHz PWM频率
    static const ledc_timer_t LEDC_TIMER = LEDC_TIMER_0;
    static const ledc_mode_t LEDC_MODE = LEDC_LOW_SPEED_MODE;
    
public:
    PwmAudioTest(gpio_num_t gpio_pin, ledc_channel_t channel = LEDC_CHANNEL_0);
    ~PwmAudioTest();
    
    bool Initialize();
    void PlayTone(uint32_t frequency, uint32_t duration_ms);
    void PlayAudioData(const int16_t* data, size_t samples);
    void Stop();
    
    bool IsInitialized() const { return is_initialized_; }
    bool IsPlaying() const { return is_playing_; }
    
private:
    void SetPwmDuty(uint32_t duty);
    uint32_t AudioSampleToPwmDuty(int16_t sample);
};

#endif // PWM_AUDIO_TEST_H
