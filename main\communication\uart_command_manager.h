#pragma once

#include <string>
#include <vector>
#include <functional>
#include "esp_err.h"
#include "text_command_storage.h"

// 前向声明
namespace text_interaction {
    class TextCommands;
}
class NetworkConsole;

namespace communication {

/**
 * @brief UART指令管理器
 * 
 * 负责处理来自UART2的文本协议指令，管理系统指令和文本指令
 */
class UartCommandManager {
public:
    /**
     * @brief 构造函数
     */
    UartCommandManager();

    /**
     * @brief 析构函数
     */
    ~UartCommandManager();

    /**
     * @brief 初始化UART指令管理器
     * @param text_commands 文本指令系统指针
     * @param network_console 网络控制台指针
     * @return esp_err_t 初始化结果
     */
    esp_err_t Initialize(text_interaction::TextCommands* text_commands, NetworkConsole* network_console);

    /**
     * @brief 处理接收到的UART数据
     * @param data 接收到的数据
     */
    void ProcessReceivedData(const std::string& data);

    /**
     * @brief 设置响应发送回调函数
     * @param callback 回调函数
     */
    void SetResponseCallback(std::function<void(const std::string&)> callback);

    /**
     * @brief 获取文本指令存储器
     * @return 文本指令存储器指针
     */
    TextCommandStorage* GetTextCommandStorage() const;

private:
    /**
     * @brief 初始化默认文本指令
     */
    void InitializeDefaultTextCommands();

    /**
     * @brief 检查是否正在进行文本交互
     * @return true 正在进行文本交互，false 没有
     */
    bool IsTextInteractionInProgress() const;
    TextCommandStorage* storage_;
    text_interaction::TextCommands* text_commands_;
    NetworkConsole* network_console_;
    std::function<void(const std::string&)> response_callback_;
    std::string receive_buffer_;
    bool initialized_;

    /**
     * @brief 处理完整的命令行
     * @param line 命令行
     */
    void ProcessCommandLine(const std::string& line);

    /**
     * @brief 处理PING命令
     */
    void HandlePing();

    /**
     * @brief 处理LIST_SYS命令
     */
    void HandleListSys();

    /**
     * @brief 处理LIST_TEXT命令
     */
    void HandleListText();

    /**
     * @brief 处理GET_SYS命令
     * @param cmd_name 系统指令名称
     */
    void HandleGetSys(const std::string& cmd_name);

    /**
     * @brief 处理GET_TEXT命令
     * @param cmd_name 文本指令名称
     */
    void HandleGetText(const std::string& cmd_name);

    /**
     * @brief 处理STATUS命令
     */
    void HandleStatus();

    /**
     * @brief 处理ADD命令
     * @param name 指令名称
     * @param text 指令内容
     */
    void HandleAdd(const std::string& name, const std::string& text);

    /**
     * @brief 处理MOD命令
     * @param name 指令名称
     * @param new_text 新的指令内容
     */
    void HandleMod(const std::string& name, const std::string& new_text);

    /**
     * @brief 处理DEL命令
     * @param name 指令名称
     */
    void HandleDel(const std::string& name);

    /**
     * @brief 处理EXEC命令
     * @param name 指令名称
     */
    void HandleExec(const std::string& name);

    /**
     * @brief 处理BACKUP命令
     */
    void HandleBackup();

    /**
     * @brief 处理RESTORE命令
     * @param data 恢复数据
     */
    void HandleRestore(const std::string& data);

    /**
     * @brief 处理RESET命令
     */
    void HandleReset();

    /**
     * @brief 发送成功响应
     * @param data 响应数据
     */
    void SendOkResponse(const std::string& data = "");

    /**
     * @brief 发送错误响应
     * @param error 错误信息
     */
    void SendErrorResponse(const std::string& error);

    /**
     * @brief 发送响应
     * @param response 响应内容
     */
    void SendResponse(const std::string& response);

    /**
     * @brief 分割字符串
     * @param str 要分割的字符串
     * @param delimiter 分隔符
     * @return std::vector<std::string> 分割结果
     */
    std::vector<std::string> SplitString(const std::string& str, char delimiter);

    /**
     * @brief 获取系统指令列表
     * @return std::vector<std::string> 系统指令列表
     */
    std::vector<std::string> GetSystemCommands();

    /**
     * @brief 获取系统指令描述
     * @param cmd_name 指令名称
     * @return std::string 指令描述
     */
    std::string GetSystemCommandDescription(const std::string& cmd_name);

    /**
     * @brief 转义字符串中的特殊字符
     * @param str 原字符串
     * @return std::string 转义后的字符串
     */
    std::string EscapeString(const std::string& str);

    /**
     * @brief 反转义字符串中的特殊字符
     * @param str 转义的字符串
     * @return std::string 反转义后的字符串
     */
    std::string UnescapeString(const std::string& str);
};

} // namespace communication
